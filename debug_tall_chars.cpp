#include "font_renderer.h"
#include <iostream>
#include <iomanip>

int main() {
    FontRenderer fontRenderer;
    if (!fontRenderer.initialize()) {
        std::cerr << "Failed to initialize font renderer" << std::endl;
        return 1;
    }
    
    if (!fontRenderer.loadFont("fonts/DroidSans.ttf", 24)) {
        std::cerr << "Failed to load font" << std::endl;
        return 1;
    }
    
    initGlobalPalette();
    
    // 测试有高上升部分的英文字符
    std::vector<std::string> testTexts = {
        "h",      // 有上升部分
        "d",      // 有上升部分
        "b",      // 有上升部分
        "k",      // 有上升部分
        "l",      // 有上升部分
        "t",      // 有上升部分
        "f",      // 有上升部分
        "hd",     // 组合测试
        "hdbl",   // 多个高字符
        "中",     // 中文对比
        "测",     // 中文对比
    };
    
    std::cout << "Tall characters analysis (with outline):" << std::endl;
    std::cout << "=========================================" << std::endl;
    
    for (const auto& text : testTexts) {
        std::cout << "Text: \"" << text << "\"" << std::endl;
        
        // 测试有描边的情况
        Bitmap bitmap = fontRenderer.renderText(text, Color::fromIndex(2), 
                                               PixelFormat::ARGB1555, 1, Color::fromIndex(3), 0);
        std::cout << "  Bitmap size (outline=1): " << bitmap.width << "x" << bitmap.height << " pixels" << std::endl;
        
        // 分析位图中实际有内容的区域
        int topMostPixel = bitmap.height;
        int bottomMostPixel = -1;
        
        for (int y = 0; y < bitmap.height; y++) {
            for (int x = 0; x < bitmap.width; x++) {
                int pixelIndex = (y * bitmap.width + x) * 2; // ARGB1555 = 2 bytes per pixel
                if (pixelIndex + 1 < bitmap.buffer.size()) {
                    uint16_t pixel = (bitmap.buffer[pixelIndex + 1] << 8) | bitmap.buffer[pixelIndex];
                    if (pixel != 0) { // 非透明像素
                        topMostPixel = std::min(topMostPixel, y);
                        bottomMostPixel = std::max(bottomMostPixel, y);
                    }
                }
            }
        }
        
        if (bottomMostPixel >= 0) {
            int actualContentHeight = bottomMostPixel - topMostPixel + 1;
            int topPadding = topMostPixel;
            int bottomPadding = bitmap.height - bottomMostPixel - 1;
            
            std::cout << "    Top padding: " << topPadding << " pixels";
            if (topPadding == 0) {
                std::cout << " ⚠️  POTENTIAL CLIPPING!";
            }
            std::cout << std::endl;
            std::cout << "    Content height: " << actualContentHeight << " pixels" << std::endl;
            std::cout << "    Bottom padding: " << bottomPadding << " pixels" << std::endl;
            
            // 检查是否有削顶风险
            if (topPadding <= 1) {
                std::cout << "    ⚠️  WARNING: Very little top padding, may have clipping!" << std::endl;
            }
        } else {
            std::cout << "    No visible content found" << std::endl;
        }
        
        std::cout << std::endl;
    }
    
    return 0;
}
