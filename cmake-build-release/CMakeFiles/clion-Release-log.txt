/home/<USER>/.local/share/JetBrains/Toolbox/apps/clion/bin/cmake/linux/x64/bin/cmake -DCMAKE_BUILD_TYPE=Release -DCMAKE_MAKE_PROGRAM=/home/<USER>/.local/share/JetBrains/Toolbox/apps/clion/bin/ninja/linux/x64/ninja -G Ninja -S /home/<USER>/workspace/projects/local/gen_font_bitmap -B /home/<USER>/workspace/projects/local/gen_font_bitmap/cmake-build-release
-- Configuring done (0.0s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/workspace/projects/local/gen_font_bitmap/cmake-build-release
