{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "/home/<USER>/.local/share/JetBrains/Toolbox/apps/clion/bin/cmake/linux/x64/bin/cmake", "cpack": "/home/<USER>/.local/share/JetBrains/Toolbox/apps/clion/bin/cmake/linux/x64/bin/cpack", "ctest": "/home/<USER>/.local/share/JetBrains/Toolbox/apps/clion/bin/cmake/linux/x64/bin/ctest", "root": "/home/<USER>/.local/share/JetBrains/Toolbox/apps/clion/bin/cmake/linux/x64/share/cmake-3.28"}, "version": {"isDirty": false, "major": 3, "minor": 28, "patch": 1, "string": "3.28.1", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-e7671b69dd7ee105ff91.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}, {"jsonFile": "cache-v2-bbce37a685029141ae31.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-cc1854ea9b4da1a5634a.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "toolchains-v1-7096c62e23c342eb06a8.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-bbce37a685029141ae31.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-cc1854ea9b4da1a5634a.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-e7671b69dd7ee105ff91.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}, "toolchains-v1": {"jsonFile": "toolchains-v1-7096c62e23c342eb06a8.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}}}