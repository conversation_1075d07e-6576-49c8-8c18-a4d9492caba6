{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Release-960a8606440dc5ac71fa.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "gen_font_bitmap", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "bin2bmp::@6890427a1f51a3e7e1df", "jsonFile": "target-bin2bmp-Release-baa4275ff47ca74a8f97.json", "name": "bin2bmp", "projectIndex": 0}, {"directoryIndex": 0, "id": "gen_font_bitmap::@6890427a1f51a3e7e1df", "jsonFile": "target-gen_font_bitmap-Release-048de0d8e27680b93fd6.json", "name": "gen_font_bitmap", "projectIndex": 0}, {"directoryIndex": 0, "id": "vz_font::@6890427a1f51a3e7e1df", "jsonFile": "target-vz_font-Release-5be9ef6481f287ca93b7.json", "name": "vz_font", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/workspace/projects/local/gen_font_bitmap/cmake-build-release", "source": "/home/<USER>/workspace/projects/local/gen_font_bitmap"}, "version": {"major": 2, "minor": 6}}