{"kind": "toolchains", "toolchains": [{"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["/usr/lib/gcc/x86_64-linux-gnu/11/include", "/usr/local/include", "/usr/include/x86_64-linux-gnu", "/usr/include"], "linkDirectories": ["/usr/lib/gcc/x86_64-linux-gnu/11", "/usr/lib/x86_64-linux-gnu", "/usr/lib", "/lib/x86_64-linux-gnu", "/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["gcc", "gcc_s", "c", "gcc", "gcc_s"]}, "path": "/usr/bin/cc", "version": "11.4.0"}, "language": "C", "sourceFileExtensions": ["c", "m"]}, {"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["/usr/include/c++/11", "/usr/include/x86_64-linux-gnu/c++/11", "/usr/include/c++/11/backward", "/usr/lib/gcc/x86_64-linux-gnu/11/include", "/usr/local/include", "/usr/include/x86_64-linux-gnu", "/usr/include"], "linkDirectories": ["/usr/lib/gcc/x86_64-linux-gnu/11", "/usr/lib/x86_64-linux-gnu", "/usr/lib", "/lib/x86_64-linux-gnu", "/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["stdc++", "m", "gcc_s", "gcc", "c", "gcc_s", "gcc"]}, "path": "/usr/bin/c++", "version": "11.4.0"}, "language": "CXX", "sourceFileExtensions": ["C", "M", "c++", "cc", "cpp", "cxx", "mm", "mpp", "CPP", "ixx", "cppm", "ccm", "cxxm", "c++m"]}], "version": {"major": 1, "minor": 0}}