#include "font_renderer.h"
#include <iostream>
#include <chrono>

int main() {
    FontRenderer fontRenderer;
    if (!fontRenderer.initialize()) {
        std::cerr << "Failed to initialize font renderer" << std::endl;
        return 1;
    }
    
    if (!fontRenderer.loadFont("fonts/DroidSans.ttf", 24)) {
        std::cerr << "Failed to load font" << std::endl;
        return 1;
    }
    
    initGlobalPalette();
    
    std::cout << "Testing Enhanced Cache System:" << std::endl;
    std::cout << "==============================" << std::endl;
    
    // 测试1：缓存大小管理
    std::cout << "\n1. Cache Size Management:" << std::endl;
    
    // 设置较小的缓存大小进行测试
    fontRenderer.setCacheSize(10, 5);  // 字形缓存10个，文本缓存5个
    
    size_t glyphCacheSize, dimensionCacheSize;
    fontRenderer.getCacheSize(glyphCacheSize, dimensionCacheSize);
    std::cout << "   Initial cache sizes - Glyph: " << glyphCacheSize 
              << ", Dimension: " << dimensionCacheSize << std::endl;
    
    // 测试2：字形缓存填充
    std::cout << "\n2. Glyph Cache Filling:" << std::endl;
    std::string testText = "Hello World 中文测试 1234567890";
    
    auto start = std::chrono::high_resolution_clock::now();
    
    // 第一次渲染（填充缓存）
    Bitmap bitmap1 = fontRenderer.renderText(testText, Color::fromIndex(2), 
                                            PixelFormat::ARGB1555, 1, Color::fromIndex(3), 0);
    
    auto mid = std::chrono::high_resolution_clock::now();
    
    // 第二次渲染（使用缓存）
    Bitmap bitmap2 = fontRenderer.renderText(testText, Color::fromIndex(2), 
                                            PixelFormat::ARGB1555, 1, Color::fromIndex(3), 0);
    
    auto end = std::chrono::high_resolution_clock::now();
    
    auto time1 = std::chrono::duration_cast<std::chrono::microseconds>(mid - start).count();
    auto time2 = std::chrono::duration_cast<std::chrono::microseconds>(end - mid).count();
    
    std::cout << "   First render (cache filling): " << time1 << " μs" << std::endl;
    std::cout << "   Second render (cache hit): " << time2 << " μs" << std::endl;
    std::cout << "   Performance improvement: " << (float)time1 / time2 << "x" << std::endl;
    
    fontRenderer.getCacheSize(glyphCacheSize, dimensionCacheSize);
    std::cout << "   Cache sizes after rendering - Glyph: " << glyphCacheSize 
              << ", Dimension: " << dimensionCacheSize << std::endl;
    
    // 测试3：缓存内存使用
    std::cout << "\n3. Cache Memory Usage:" << std::endl;
    size_t memoryUsage = fontRenderer.getCacheMemoryUsage();
    std::cout << "   Total cache memory usage: " << memoryUsage << " bytes" << std::endl;
    std::cout << "   Average per glyph: " << (glyphCacheSize > 0 ? memoryUsage / glyphCacheSize : 0) << " bytes" << std::endl;
    
    // 测试4：缓存溢出处理
    std::cout << "\n4. Cache Overflow Handling:" << std::endl;
    std::cout << "   Rendering many different characters to test cache overflow..." << std::endl;
    
    // 渲染大量不同字符以测试缓存溢出
    for (int i = 0; i < 50; i++) {
        std::string singleChar = std::string(1, 'A' + (i % 26));
        fontRenderer.renderText(singleChar, Color::fromIndex(2), 
                               PixelFormat::ARGB1555, 1, Color::fromIndex(3), 0);
    }
    
    fontRenderer.getCacheSize(glyphCacheSize, dimensionCacheSize);
    std::cout << "   Cache sizes after overflow test - Glyph: " << glyphCacheSize 
              << ", Dimension: " << dimensionCacheSize << std::endl;
    
    size_t newMemoryUsage = fontRenderer.getCacheMemoryUsage();
    std::cout << "   Memory usage after overflow: " << newMemoryUsage << " bytes" << std::endl;
    
    // 测试5：文本尺寸缓存
    std::cout << "\n5. Text Dimension Cache:" << std::endl;
    
    std::vector<std::string> testTexts = {
        "Test 1",
        "Test 2 中文",
        "Test 3 longer text",
        "Test 4 更长的中文文本",
        "Test 5 mixed 混合 text"
    };
    
    // 第一次计算（填充缓存）
    start = std::chrono::high_resolution_clock::now();
    for (const auto& text : testTexts) {
        int w, h;
        fontRenderer.getTextDimensions(text, 1, w, h, 0);
    }
    mid = std::chrono::high_resolution_clock::now();
    
    // 第二次计算（使用缓存）
    for (const auto& text : testTexts) {
        int w, h;
        fontRenderer.getTextDimensions(text, 1, w, h, 0);
    }
    end = std::chrono::high_resolution_clock::now();
    
    auto dimTime1 = std::chrono::duration_cast<std::chrono::microseconds>(mid - start).count();
    auto dimTime2 = std::chrono::duration_cast<std::chrono::microseconds>(end - mid).count();
    
    std::cout << "   First dimension calculation: " << dimTime1 << " μs" << std::endl;
    std::cout << "   Second dimension calculation: " << dimTime2 << " μs" << std::endl;
    std::cout << "   Performance improvement: " << (float)dimTime1 / dimTime2 << "x" << std::endl;
    
    fontRenderer.getCacheSize(glyphCacheSize, dimensionCacheSize);
    std::cout << "   Final cache sizes - Glyph: " << glyphCacheSize 
              << ", Dimension: " << dimensionCacheSize << std::endl;
    
    // 测试6：缓存清空
    std::cout << "\n6. Cache Clearing:" << std::endl;
    fontRenderer.clearCache();
    fontRenderer.getCacheSize(glyphCacheSize, dimensionCacheSize);
    std::cout << "   Cache sizes after clearing - Glyph: " << glyphCacheSize 
              << ", Dimension: " << dimensionCacheSize << std::endl;
    std::cout << "   Memory usage after clearing: " << fontRenderer.getCacheMemoryUsage() << " bytes" << std::endl;
    
    std::cout << "\n✓ Enhanced cache system test completed!" << std::endl;
    
    return 0;
}
