
#include "char_code.h"
#include "char_code_c.h"

#include <iostream>

namespace vzes {

bool IsUtf8String(const std::string &str) {
    return ::CheckUtf8String(str.c_str(), str.size());
}

bool Gb2312ToUtf8(std::string &outStr_, const std::string &inputStr) {
    if (IsUtf8String(inputStr)) {
        outStr_ = inputStr;
        return true;
    }
    
    std::string outStr;
    outStr.resize(inputStr.size() * 4 + 1);
    int new_size = RGN_Gb2312_To_UTF8((unsigned char *) outStr.c_str(), outStr.size(),
                                      (unsigned char *) inputStr.c_str(), inputStr.size());
    outStr.resize(new_size);

    outStr_.swap(outStr);

    return true;
}

std::string Gb2312ToUtf8(const std::string &src) {
    std::string dst;
    if (Gb2312ToUtf8(dst, src)) {
        return dst;
    }
    return std::string();
}

bool Utf8ToGb2312(std::string &outStr_, const std::string &inputStr) {
    if (IsUtf8String(inputStr)) {
        std::string outStr;
        outStr.resize(inputStr.size() * 4 + 1);
        int new_size = RGN_UTF8_To_Gb2312((unsigned char *) outStr.c_str(), outStr.size(),
                                          (unsigned char *) inputStr.c_str(), inputStr.size());
        outStr.resize(new_size);
        outStr_.swap(outStr);
        return true;
    }

    outStr_ = inputStr;
    return true;
}

std::string Utf8ToGb2312(const std::string &src) {
    std::string dst;
    if (Utf8ToGb2312(dst, src)) {
        return dst;
    }
    return std::string();
}

int RGN_Gb2312_To_UTF8(unsigned char *pOut, int pOutLen,
                       unsigned char *pText, int pLen) {
    return ::Gb2312_To_UTF8(pOut, pOutLen, pText, pLen);
}

int RGN_UTF8_To_Gb2312(unsigned char *pOut, int pOutLen,
                       unsigned char *pText, int pLen) {
    return ::UTF8_To_Gb2312(pOut, pOutLen, pText, pLen);
}

void GBcode_Unicode(unsigned char *pout, const unsigned char *pintput) {
  ::GBcode_Unicode(pout, pintput);
}

void Unicode_GBcode(unsigned char *pout, const unsigned char *pintput) {
  ::Unicode_GBcode(pout, pintput);
}
}
