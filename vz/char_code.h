/*
 * vzsdk
 * Copyright 2013 - 2018, Vzenith Inc.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 *  1. Redistributions of source code must retain the above copyright notice,
 *     this list of conditions and the following disclaimer.
 *  2. Redistributions in binary form must reproduce the above copyright notice,
 *     this list of conditions and the following disclaimer in the documentation
 *     and/or other materials provided with the distribution.
 *  3. The name of the author may not be used to endorse or promote products
 *     derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO
 * EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF
 * ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */


/*
Copyright(C) 1999 Aladdin Enterprises.All rights reserved.

This software is provided 'as-is', without any express or implied
warranty.In no event will the authors be held liable for any damages
arising from the use of this software.

Permission is granted to anyone to use this software for any purpose,
including commercial applications, and to alter it and redistribute it
freely, subject to the following restrictions :

1. The origin of this software must not be misrepresented; you must not
claim that you wrote the original software.If you use this software
in a product, an acknowledgment in the product documentation would be
appreciated but is not required.
2. Altered source versions must be plainly marked as such, and must not be
misrepresented as being the original software.
3. This notice may not be removed or altered from any source distribution.

L.Peter Deutsch
<EMAIL>

Independent implementation of MD5 (RFC 1321).

This code implements the MD5 Algorithm defined in RFC 1321.
It is derived directly from the text of the RFC and not from the
reference implementation.

The original and principal author of md5.h is L. Peter Deutsch
<<EMAIL>>.  Other authors are noted in the change history
that follows (in reverse chronological order):

1999-11-04 lpd Edited comments slightly for automatic TOC extraction.
1999-10-18 lpd Fixed typo in header comment (ansi2knr rather than md5);
added conditionalization for C++ compilation from Martin
Purschke <<EMAIL>>.
1999-05-03 lpd Original version.
*/

#pragma once

#include <string>

namespace vzes {

//检测编码是否符合UTF8规则，可以用于检测是否是UTF8字符串。纯英文字符串一定返回True。
//注意：GB2312/GBK字符串也有小概率符合UTF8编码规则，调此接口小概率返回True。
bool IsUtf8String(const std::string &str);
std::string Gb2312ToUtf8(const std::string &src);
std::string Utf8ToGb2312(const std::string &src);
bool Gb2312ToUtf8(std::string &outStr, const std::string &inputStr);
bool Utf8ToGb2312(std::string &outStr, const std::string &inputStr);
void GBcode_Unicode(unsigned char *pout, const unsigned char *pintput);
void Unicode_GBcode(unsigned char *pout, const unsigned char *pintput);

//GB2312 תΪ UTF-8
int RGN_Gb2312_To_UTF8(unsigned char *pOut, int pOutLen,
                       unsigned char *pText, int pLen);
//UTF-8 תΪ GB2312
int RGN_UTF8_To_Gb2312(unsigned char *pOut, int pOutLen,
                       unsigned char *pText, int pLen);

}
