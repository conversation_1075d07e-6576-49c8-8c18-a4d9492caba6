#include "chartab.h"
#include "fttav.h"
#include "../src/bin2bmp.h"
#include <fstream>

using namespace SAL;

int main(int argc, char *argv[]) {
    int32_t ret = 0;

    // std::string font_path = "./fonts/DroidSans.ttf";
    std::string font_path = "./fonts/taiwen.ttf";
    if (ftInit(font_path.c_str()) != ft_true) {
        printf("ftInit failed, font path: %s\n", font_path.c_str());
        return -1;
    }

    auto vz_char_table = VzCharTab_Create(96);
    if (!vz_char_table) {
        printf("VzCharTab_Create failed\n");
        ftFree();
        return -1;
    }

    int font_size = 96;
    int font_space = 1;
    int font_color = 1; // 0: white, 1: red, 2: blue, 3: green
    CanvasPixelFormat pix_fmt = CANVAS_PIXEL_FORMAT_ARGB_1555;
    void *bmp_data = nullptr;
    int bmp_width = 0, bmp_height = 0;
    do {
        // 测试不同的泰文字符串
        std::vector<std::string> test_strings = {
            "Hello World",  // 英文测试
            "กา",           // 简单泰文
            "กั่",         // 泰文 + 上方符号
            "กุ",           // 泰文 + 下方符号
            "กั่กุ",       // 组合测试
            "ที่ยนื่อตั้ต้เพื่กุมูญฎฏฐปิป๊ฟีฝื่ก่าเกแจโอแตะโดะกำ๐๑๒๓๔๕๖๗๘๙งนมำกดบ"  // 完整泰文测试
        };

        for (size_t i = 0; i < test_strings.size(); i++) {
            std::string test_font_str = test_strings[i];
            printf("\n=== Test %zu: %s ===\n", i + 1, test_font_str.c_str());

            ret = VzCharTab_Get_Char_Width(vz_char_table, font_size, font_space, test_font_str.c_str(), bmp_width, bmp_height);
            if (ret < 0) {
                printf("VzCharTab_Get_Char_Width failed\n");
                continue;
            }
            printf("Calculated dimensions - width: %d, height: %d\n", bmp_width, bmp_height);

            if (bmp_data) {
                free(bmp_data);
                bmp_data = nullptr;
            }
            bmp_data = calloc(1, bmp_width * bmp_height * 2);

            ret = VzCharTab_GetBmp(vz_char_table, font_size, font_space, test_font_str.c_str(), (uint16_t*)bmp_data, bmp_width, font_color, pix_fmt);
            if (ret < 0) {
                printf("VzCharTab_GetBmp failed\n");
                continue;
            }

            // 保存每个测试的结果
            char filename[256];
            snprintf(filename, sizeof(filename), "vz_font_test_%zu.bin", i + 1);
            std::ofstream ofs(filename, std::ios::binary);
            if (ofs) {
                ofs.write((char*)bmp_data, bmp_width * bmp_height * 2);
                printf("Saved %s, w: %d, h: %d\n", filename, bmp_width, bmp_height);

                // 转换为BMP
                BinToBMP converter;
                char bmp_filename[256];
                snprintf(bmp_filename, sizeof(bmp_filename), "vz_font_test_%zu.bmp", i + 1);
                if (converter.convert(filename, bmp_filename, bmp_width, bmp_height, PixelFormat::ARGB1555, "")) {
                    printf("Converted to %s\n", bmp_filename);
                } else {
                    printf("Error converting to BMP\n");
                }
            }
        }
    } while(0);

    if (bmp_data) {
        free(bmp_data);
    }

    printf("\n=== Thai Font Rendering Test Completed ===\n");

    VzCharTab_Destroy(vz_char_table);

    ftFree();

    return 0;
}
