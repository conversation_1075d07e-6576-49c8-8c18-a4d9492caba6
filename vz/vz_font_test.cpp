#include "chartab.h"
#include "fttav.h"
#include "../src/bin2bmp.h"
#include <fstream>

using namespace SAL;

int main(int argc, char *argv[]) {
    int32_t ret = 0;

    // std::string font_path = "./fonts/DroidSans.ttf";
    std::string font_path = "./fonts/taiwen.ttf";
    if (ftInit(font_path.c_str()) != ft_true) {
        printf("ftInit failed, font path: %s\n", font_path.c_str());
        return -1;
    }

    auto vz_char_table = VzCharTab_Create(96);
    if (!vz_char_table) {
        printf("VzCharTab_Create failed\n");
        ftFree();
        return -1;
    }

    int font_size = 96;
    int font_space = 1;
    int font_color = 1; // 0: white, 1: red, 2: blue, 3: green
    CanvasPixelFormat pix_fmt = CANVAS_PIXEL_FORMAT_ARGB_1555;
    void *bmp_data = nullptr;
    int bmp_width = 0, bmp_height = 0;
    do {
        // std::string test_font_str = "Hello World";
        std::string test_font_str = "ที่ยนื่อตั้ต้เพื่กุมูญฎฏฐปิป๊ฟีฝื่ก่าเกแจโอแตะโดะกำ๐๑๒๓๔๕๖๗๘๙งนมำกดบ";

        ret = VzCharTab_Get_Char_Width(vz_char_table, font_size, font_space, test_font_str.c_str(), bmp_width, bmp_height);
        if (ret < 0) {
            printf("VzCharTab_Get_Char_Width failed\n");
            break;
        }
        bmp_data = calloc(1, bmp_width * bmp_height * 2);

        ret = VzCharTab_GetBmp(vz_char_table, font_size, font_space, test_font_str.c_str(), (uint16_t*)bmp_data, bmp_width, font_color, pix_fmt);
        if (ret < 0) {
            printf("VzCharTab_GetBmp failed\n");
            break;
        }
    } while(0);

    if (bmp_data) {
        std::ofstream ofs("vz_font_bmp.bin");
        if (ofs) {
            ofs.write((char*)bmp_data, bmp_width * bmp_height * 2);
        }
        printf("write bmp bin done, w: %d, h: %d\n", bmp_width, bmp_height);
        // 创建BinToBMP对象
        BinToBMP converter;

        // 转换并保存BMP文件
        if (!converter.convert("vz_font_bmp.bin", "vz_font.bmp", bmp_width, bmp_height, PixelFormat::ARGB1555, "")) {
            printf("Error converting to BMP\n");
        }

        free(bmp_data);
    }

    VzCharTab_Destroy(vz_char_table);

    ftFree();

    return 0;
}
