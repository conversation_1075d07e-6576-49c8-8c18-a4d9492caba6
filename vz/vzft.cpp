#include "fttav.h"
#include <wchar.h>
#include <locale.h>
#include <ctype.h>
#include <stdio.h>
#include <stdlib.h>

#include "ft2build.h"
#include "chartab.h"
#include FT_FREETYPE_H

#include FT_BITMAP_H
#include FT_GLYPH_H

namespace SAL {

static FT_Library ftlibrary = 0;
static FT_Face ft_face = 0;
static int g_fontheight = 0;
static int isFtFaceInitialized = false;

static ft_bool ftSetFontSize(int h_c) {
  if (h_c <= 0) {
    JMSG("invalide paragma!");
    return ft_false;
  }
  if (ft_face == 0) {
    JMSG("uninited!");
    return ft_false;
  }
  if (g_fontheight == h_c) {
    return ft_true;
  }
  // printf("set freetype pixel size, face %08x,size %d\n",ft_face,h_c);
  g_fontheight = h_c;
  FT_Error error = FT_Set_Pixel_Sizes(ft_face, h_c, h_c);
  if (error) {
    JMSG("Failed init font size [%d %d]", h_c, h_c);
    return ft_false;
  }
  return ft_true;
}

// temp buffer, max size
// FIXME: 暂时不知道这个buffer是怎么计算的
static unsigned char yuv8bit_buffer[30720];
/*****************************************
font_height: 16, 24, 48, 64, 96
***********************************/
static int DrawCharToYuv(FT_GlyphSlot slot, SAL::FONT_DATA_BUFFER *pdatabuffer, int blackbordersize,
                         unsigned int wkey) {
  unsigned char *pdata, *pdst;
  FT_Bitmap *ft_bmp = &slot->bitmap;

  int font_height = ft_face->size->metrics.height >> 6;  // max height
  int font_width = (slot->advance.x >> 6);
  int topblank = (ft_face->size->metrics.ascender >> 6) - slot->bitmap_top;
  int leftblank = slot->bitmap_left;
  int blackbordersizeall = blackbordersize << 1;

  if (ft_bmp->pixel_mode != FT_PIXEL_MODE_GRAY) {
    return -1;
  }
  unsigned char *ft_bmp_buff;
  int pitch = ft_bmp->pitch;  //
  int width = ft_bmp->width;  //
  int height = ft_bmp->rows;  //
  printf("---pitch: %d, width: %d, height: %d\n", pitch, width, height);

  // leftblank < 0
#if 0
    if (leftblank < 0) {
        if (width - leftblank > font_width)
            font_width = width - leftblank;
        leftblank = 0;
    }
#endif
  // when ' ', width / 2
  if ((wkey == 0x20) && (width == 0)) {
    font_width = font_width >> 1;
  }

  if (width == 0 || height == 0 || topblank < 0) {
    // JMSG("width:%d,height:%d,topblank:%d,leftblank:%d,font_width:%d,font_height:%d\n",
    //    width, height, topblank, leftblank, font_width, font_height);
  }
  // set bitmap with 0, means transparent
  memset(yuv8bit_buffer, 0, sizeof(yuv8bit_buffer));
  // allcate buffer
  pdatabuffer->step = ((font_width + blackbordersizeall + 3) >> 2) << 2;
  pdatabuffer->width = font_width + blackbordersizeall;
  pdatabuffer->offset = font_width;
  pdatabuffer->height = font_height;  // don't need add border, this num is large
  // FIXME: why >> 2 not >> 1 ?
  pdatabuffer->pdatabuf = (unsigned char *)calloc(sizeof(unsigned char), (pdatabuffer->step * pdatabuffer->height) >> 2);

  // add black border, fix 2 pixel border
  // ft_bmp_buff = ft_bmp->buffer;
  // leftblank += blackbordersize;
  // topblank += blackbordersize;
  // for (int j = 0; j < height; j++) {
  //   for (int i = 0; i < width; i++) {
  //     if (ft_bmp_buff[i] != 0) {
  //       for (int q = -blackbordersize; q <= blackbordersize; q++) {
  //         for (int p = -blackbordersize; p <= blackbordersize; p++) {
  //           int y = j + q + topblank;
  //           int x = i + p + leftblank;
  //           if (y >= 0 && y < pdatabuffer->height && x >= 0 && x < pdatabuffer->width) {
  //             // set border color index with 1, means black
  //             yuv8bit_buffer[y * pdatabuffer->step + x] = 1;
  //           }
  //         }
  //       }
  //     }
  //   }
  //   ft_bmp_buff += pitch;
  // }

  // convert 8bit[0~255] grays to 2bit[0~3] color index
  // transparent -> 0
  // border -> 1
  // gray[1~112] -> 2
  // gray(112~255] -> 3
  // 最终的颜色会根据 I2GrayToRGB1555Lut 中定义的值进行转换
  ft_bmp_buff = ft_bmp->buffer;
  pdata = yuv8bit_buffer + pdatabuffer->step * topblank;
  for (int j = 0; j < height && j < (pdatabuffer->height - topblank); j++) {
    for (int i = 0; i < width; i++) {
      if (ft_bmp_buff[i] != 0) {
        if (ft_bmp_buff[i] > 0x70)
          pdata[i + leftblank] = 3;
        else
          pdata[i + leftblank] = 2;
      }
    }
    pdata += pdatabuffer->step;
    ft_bmp_buff += pitch;
  }

  // convert 2bit data to 8bit data, means 1byte contains 4 pixels
  pdata = yuv8bit_buffer;
  pdst = pdatabuffer->pdatabuf;
  for (int j = 0; j < pdatabuffer->height; j++) {
    for (int i = 0, k = 0; i<(pdatabuffer->width + 3)>> 2; i++) {
      pdst[i] = pdata[k] | (pdata[k + 1] << 2) | (pdata[k + 2] << 4) | (pdata[k + 3] << 6);
      k += 4;
    }
    pdata += pdatabuffer->step;
    pdst += pdatabuffer->step >> 2;
  }
  return 0;
}
//
// int ftGetCharBitMap(int fontsize, int blackbordersize, unsigned short wkey, FONT_DATA_BUFFER
// *pdatabuffer) {
//    if (!ft_face || !ftlibrary || !pdatabuffer) {
//        JMSG("error ft_face or ftlibrary");
//        return -1;
//    }
//
//    if (pdatabuffer->pdatabuf) {
//        delete pdatabuffer->pdatabuf;
//        pdatabuffer->pdatabuf = NULL;
//    }
//    memset(pdatabuffer, 0, sizeof(FONT_DATA_BUFFER));
//
//    FT_UInt current_index = 0;
//    FT_GlyphSlot slot = ft_face->glyph;
//    ftSetFontSize(fontsize);
//
//    if (wkey > 0x80) {
//        unsigned short wc;
//        GBcode_Unicode((unsigned char*)&wc, (unsigned char*)&wkey);
//        wc = ((wc & 0xff) << 8) + (wc >> 8);
//        current_index = FT_Get_Char_Index(ft_face, wc);
//    }
//    else {
//        current_index = FT_Get_Char_Index(ft_face, wkey);
//    }
//    if (FT_Load_Glyph(ft_face, current_index,
//        FT_LOAD_RENDER)) {
//        JMSG("Init FT_Load_Glyph error %04x", wkey);
//        return -1;
//    }
///*        if (slot->format != FT_GLYPH_FORMAT_BITMAP) {
//            if (FT_Render_Glyph(ft_face->glyph, FT_RENDER_MODE_NORMAL)) {
//                JMSG("Init FT_Render_Glyph error");
//                return -1;
//            }
//        }*/
//    return DrawCharToYuv(slot, pdatabuffer, blackbordersize, wkey);
//}

int ftGetCharBitMap(int fontsize, int blackbordersize, unsigned int wkey,
                    FONT_DATA_BUFFER *pdatabuffer) {
  if (!ft_face || !ftlibrary || !pdatabuffer) {
    JMSG("error ft_face or ftlibrary");
    return -1;
  }
  // printf("ft get char bit map \n");
  if (pdatabuffer->pdatabuf) {
    free(pdatabuffer->pdatabuf);
    pdatabuffer->pdatabuf = NULL;
  }
  memset(pdatabuffer, 0, sizeof(SAL::FONT_DATA_BUFFER));

  FT_UInt current_index = 0;
  FT_GlyphSlot slot = ft_face->glyph;
  // printf("ft set font size \n");
  if (ft_true != ftSetFontSize(fontsize)) {
    printf("ft set font size to %d  failed\n", fontsize);
  }
  // printf("ft get char index \n");
  current_index = FT_Get_Char_Index(ft_face, wkey);
  // printf("wkey %04X char index %d\n",wkey,current_index);
  FT_Error ret;
  ret = FT_Load_Glyph(ft_face, current_index, FT_LOAD_RENDER);
  if (ret) {
    JMSG("Init FT_Load_Glyph error %04x, error code 0x%X", wkey, ret);
    return -1;
  }
  /*        if (slot->format != FT_GLYPH_FORMAT_BITMAP) {
  if (FT_Render_Glyph(ft_face->glyph, FT_RENDER_MODE_NORMAL)) {
  JMSG("Init FT_Render_Glyph error");
  return -1;
  }
  }*/
  // printf("draw to yuv \n");
  return DrawCharToYuv(slot, pdatabuffer, blackbordersize, wkey);
}

ft_bool ftInit(const char *filename) {
  if (filename == NULL) {
    JMSG("invalid param!");
    return ft_false;
  }

  FT_Error error;
  if (isFtFaceInitialized) {
    JMSG("FT library has been initialized");
    return ft_true;
  }
  do {
    error = FT_Init_FreeType(&ftlibrary);
    if (error) {
      JMSG("Failed to intialize freetype library");
      break;
    }

    error = FT_New_Face(ftlibrary, filename, 0, &ft_face);
    if (error) {
      JMSG("Failed to load font %s", filename);
      break;
    }

    error = FT_Select_Charmap(ft_face, FT_ENCODING_UNICODE);
    if (error) {
      JMSG("Invalid charmap [%d]", FT_ENCODING_UNICODE);
      break;
    }
    if (ftSetFontSize(16) != ft_true) {
      JMSG("Failed init font size [16 16]");
      break;
    }
  } while (0);

  isFtFaceInitialized = true;
  return ft_true;
}

void ftFree() {
  isFtFaceInitialized = false;
  if (ft_face) {
    FT_Done_Face(ft_face);
    ft_face = NULL;
  }
  if (ftlibrary) {
    FT_Done_FreeType(ftlibrary);
    ftlibrary = NULL;
  }
  g_fontheight = 0;
}
}