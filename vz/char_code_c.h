#ifndef __CHAR_CODE_C_H__
#define __CHAR_CODE_C_H__

#include "stdbool.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* End of #ifdef __cplusplus */

bool CheckUtf8String(const char *str, int size);

void GBcode_Unicode(unsigned char *pout, const unsigned char *pintput);

void Unicode_GBcode(unsigned char *pout, const unsigned char *pintput);

int Gb2312_To_UTF8(unsigned char *pOut, int pOutLen,
                       unsigned char *pText, int pLen);
int UTF8_To_Gb2312(unsigned char *pOut, int pOutLen,
                       unsigned char *pText, int pLen);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* End of #ifdef __cplusplus */

#endif