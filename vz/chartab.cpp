
#include "chartab.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "fttav.h"
#include "char_code_c.h"
#include <list>
#include <vector>
#include <fstream>
namespace SAL {

#ifdef OPTIMIZE_FONT_MEM_SIZE
#define ASC_OTHER_CACHE_NUM         1
#define GB2312_OTHER_CACHE_NUM      1
#else
#define ASC_OTHER_CACHE_NUM         20
#define GB2312_OTHER_CACHE_NUM      50
#endif

#ifdef ENABLE_CUSTOM_OSD_COLOR
#define MAX_FONT_COLOR_TYPE 15
#else
#define MAX_FONT_COLOR_TYPE 4
#endif

VzCharTable *g_char_tab_ = NULL;
std::mutex g_chartab_mutex_;
std::mutex g_font_mutex_;

static const uint8_t PLATE_GB2312_LIST[] = {
  0xBE, 0xA9, 0xBD, 0xF2, 0xBC, 0xBD, 0xBD, 0xFA, // 京津冀晋
  0xC3, 0xC9, 0xC1, 0xC9, 0xBC, 0xAA, 0xBA, 0xDA, // 蒙辽吉黑
  0xBB, 0xA6, 0xCB, 0xD5, 0xD5, 0xE3, 0xCD, 0xEE, // 沪苏浙皖
  0xC3, 0xF6, 0xB8, 0xD3, 0xC2, 0xB3, 0xD4, 0xA5, // 闽赣鲁豫
  0xB6, 0xF5, 0xCF, 0xE6, 0xD4, 0xC1, 0xB9, 0xF0, // 鄂湘粤桂
  0xC7, 0xED, 0xD3, 0xE5, 0xB4, 0xA8, 0xB9, 0xF3, // 琼渝川贵
  0xD4, 0xC6, 0xB2, 0xD8, 0xC9, 0xC2, 0xB8, 0xCA, // 云藏陕甘
  0xC7, 0xE0, 0xC4, 0xFE, 0xD0, 0xC2, 0xB8, 0xDB, // 青宁新港
  0xB0, 0xC4, 0xCC, 0xA8, 0xBE, 0xAF, 0xCA, 0xB9, // 澳台警使
  0xC1, 0xEC, 0xD1, 0xA7, 0xB5, 0xE7, 0xB3, 0xA7, // 领学电厂
  0xB3, 0xA1, 0xC4, 0xDA,                         // 场内

  0xCF, 0xF1, 0xCB, 0xD8, 0xC6, 0xE4, 0xCB, 0xFB, // 像素其他
  0xCE, 0xB4, 0xD6, 0xAA, 0xD5, 0xE6, 0xBC, 0xD9, // 未知真假
  0xCE, 0xDE, 0xC9, 0xAB,                         // 无色

  0xC0, 0xB6, 0xBB, 0xC6, 0xB0, 0xD7, 0xBA, 0xDA, // 蓝黄白黑
  0xC2, 0xCC, 0xD2, 0xF8, 0xB7, 0xDB, 0xBA, 0xEC, // 绿银粉红
  0xD7, 0xD8, 0xBB, 0xD2, 0x00                    // 棕灰
};
static const char PLATE_ASC_LIST[] = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567889`~!@#$%^&*()-_=+{}[]|;:\"',./<>? ";

/*
 * NOTE: 如需支持更大的字体size, 需要调整vzft.cpp中如下静态缓冲区大小
 * static unsigned char yuv8bit_buffer[20480];
 */
std::list<uint32_t> g_font_size;
std::vector<FontCharType> g_font_char_type = {FONT_CHAR_ASCII, FONT_CHAR_ASCII_OTHER, FONT_CHAR_GB2312_PLATE, FONT_CHAR_GB2312_OTHER};

const int FONT_GB2312_LENGTH = 2;
const int FONT_ASC_LENGTH = 1;

static unsigned short GB2312_Get_Font_Index(const unsigned char* pText) {
  return ((pText[1]) << 8) + (pText[0]);
}

int GetUTF8_Bytes(const unsigned char * pInput) {
  if((pInput[0] & 0x80) == 0) {
    return 1;
  } else if((pInput[0] >= 0xFC) && (pInput[0] <= 0xFD)) {
    return 6;
  } else if(pInput[0] >= 0xF8) {
    return 5;
  } else if(pInput[0] >= 0xF0) {
    return 4;
  } else if(pInput[0] >= 0xE0) {
    return 3;
  } else if(pInput[0] >= 0xC0) {
    return 2;
  }
  return 0;
}

int ToUnicodeKey(const unsigned char * pInput, int len,unsigned int & key) {
  if ((pInput == NULL) || (len == 0)) {
    return -1;
  }
  key = 0;
  int nBytes = GetUTF8_Bytes(pInput);
  if(nBytes > len ) {
    printf("The length is %d, which is less then UTF-8 BYTEs %d\n",len,nBytes);
    return -1;
  }
  switch (nBytes) {
  case 0:
    printf("Get UTF-8 string length failed, zero bytes...input len %d, content %s\n",len,pInput);
    return -1;
  case 1:
    key = pInput[0] & 0x7F;
    break;
  case 2:
    if ((pInput[1] & 0xC0) != 0x80) {
      printf("invalid utf-8 string, 2 bytes \n");
      return 0;
    }
    key = pInput[0] & 0x1F;
    key = (key << 6) | (pInput[1] & 0x3F);
    break;
  case 3:
    if ((pInput[1] & 0xC0) != 0x80 || (pInput[2] & 0xC0) != 0x80) {
      printf("invalid utf-8 string, 3 bytes \n");
      return 0;
    }
    key = pInput[0] & 0x0F;
    key = (key << 6) | (pInput[1] & 0x3F);
    key = (key << 6) | (pInput[2] & 0x3F);
    break;
  case 4:
    if ((pInput[1] & 0xC0) != 0x80 || (pInput[2] & 0xC0) != 0x80
        || (pInput[3] & 0xC0) != 0x80) {
      printf("invalid utf-8 string, 4 bytes \n");
      return 0;
    }
    key = pInput[0] & 0x07;
    key = (key << 6) | (pInput[1] & 0x3F);
    key = (key << 6) | (pInput[2] & 0x3F);
    key = (key << 6) | (pInput[3] & 0x3F);
    break;
  case 5:
    if ((pInput[1] & 0xC0) != 0x80 || (pInput[2] & 0xC0) != 0x80
        || (pInput[3] & 0xC0) != 0x80 || (pInput[4] & 0xC0) != 0x80 ) {
      printf("invalid utf-8 string, 5 bytes \n");
      return 0;
    }
    key = pInput[0] & 0x03;
    key = (key << 6) | (pInput[1] & 0x3F);
    key = (key << 6) | (pInput[2] & 0x3F);
    key = (key << 6) | (pInput[3] & 0x3F);
    key = (key << 6) | (pInput[4] & 0x3F);
    break;

  case 6:
    if ((pInput[1] & 0xC0) != 0x80 || (pInput[2] & 0xC0) != 0x80 || (pInput[3] & 0xC0) != 0x80
        || (pInput[4] & 0xC0) != 0x80 || (pInput[5] & 0xC0) != 0x80) {
      printf("invalid utf-8 string, 6 bytes \n");
      return 0;
    }
    key = pInput[0] & 0x01;
    key = (key << 6) | (pInput[1] & 0x3F);
    key = (key << 6) | (pInput[2] & 0x3F);
    key = (key << 6) | (pInput[3] & 0x3F);
    key = (key << 6) | (pInput[4] & 0x3F);
    key = (key << 6) | (pInput[5] & 0x3F);
    break;
  default:
    printf("Get UTF-8 string length failed, length %d is not support ...\n",nBytes);
    nBytes=0;
    break;
  }
  return nBytes;
}

// 0: transparent, 1: stroke color, 2: middle color, 3：full color
const unsigned short I2GrayToRGB1555Lut[MAX_FONT_COLOR_TYPE][4] = {
  { 0, 0x8421, 0xc210, 0xffff }, // white 白
  { 0, 0x8400, 0xc000, 0xfc00 }, // red 红
  { 0, 0x8001, 0x8010, 0x801f }, // blue 蓝
  { 0, 0x8020, 0x8200, 0x83e0 }  // green 绿
  // TODO: support custom color
};

#ifdef ENABLE_CUSTOM_OSD_COLOR
const unsigned short I2GrayToClut4[MAX_FONT_COLOR_TYPE][4] = {
  {CLUT4_COLOR_FULL_TRANS, CLUT4_COLOR_FULL_BLACK, CLUT4_COLOR_FULL_WHITE, CLUT4_COLOR_FULL_WHITE}, // white 白
  {CLUT4_COLOR_FULL_TRANS, CLUT4_COLOR_FULL_BLACK, CLUT4_COLOR_FULL_RED, CLUT4_COLOR_FULL_RED}, // red 红
  {CLUT4_COLOR_FULL_TRANS, CLUT4_COLOR_FULL_BLACK, CLUT4_COLOR_FULL_BLUE, CLUT4_COLOR_FULL_BLUE}, // blue 蓝
  {CLUT4_COLOR_FULL_TRANS, CLUT4_COLOR_FULL_BLACK, CLUT4_COLOR_FULL_GREEN, CLUT4_COLOR_FULL_GREEN},  // green 绿
  {CLUT4_COLOR_FULL_TRANS, CLUT4_COLOR_FULL_WHITE, CLUT4_COLOR_FULL_BLACK, CLUT4_COLOR_FULL_BLACK},  // black 黑

  {CLUT4_COLOR_FULL_TRANS, CLUT4_COLOR_FULL_BLACK, CLUT4_COLOR_CUSTOM_0, CLUT4_COLOR_CUSTOM_0},
  {CLUT4_COLOR_FULL_TRANS, CLUT4_COLOR_FULL_BLACK, CLUT4_COLOR_CUSTOM_1, CLUT4_COLOR_CUSTOM_1},
  {CLUT4_COLOR_FULL_TRANS, CLUT4_COLOR_FULL_BLACK, CLUT4_COLOR_CUSTOM_2, CLUT4_COLOR_CUSTOM_2},
  {CLUT4_COLOR_FULL_TRANS, CLUT4_COLOR_FULL_BLACK, CLUT4_COLOR_CUSTOM_3, CLUT4_COLOR_CUSTOM_3},
  {CLUT4_COLOR_FULL_TRANS, CLUT4_COLOR_FULL_BLACK, CLUT4_COLOR_CUSTOM_4, CLUT4_COLOR_CUSTOM_4},
  {CLUT4_COLOR_FULL_TRANS, CLUT4_COLOR_FULL_BLACK, CLUT4_COLOR_CUSTOM_5, CLUT4_COLOR_CUSTOM_5},
  {CLUT4_COLOR_FULL_TRANS, CLUT4_COLOR_FULL_BLACK, CLUT4_COLOR_CUSTOM_6, CLUT4_COLOR_CUSTOM_6},
  {CLUT4_COLOR_FULL_TRANS, CLUT4_COLOR_FULL_BLACK, CLUT4_COLOR_CUSTOM_7, CLUT4_COLOR_CUSTOM_7},
  {CLUT4_COLOR_FULL_TRANS, CLUT4_COLOR_FULL_BLACK, CLUT4_COLOR_CUSTOM_8, CLUT4_COLOR_CUSTOM_8},
  {CLUT4_COLOR_FULL_TRANS, CLUT4_COLOR_FULL_BLACK, CLUT4_COLOR_CUSTOM_9, CLUT4_COLOR_CUSTOM_9},
};
#else
const unsigned short I2GrayToClut4[MAX_FONT_COLOR_TYPE][4] = {
  {CLUT4_COLOR_FULL_TRANS, CLUT4_COLOR_STROKE_WHITE, CLUT4_COLOR_MIDDLE_WHITE, CLUT4_COLOR_FULL_WHITE}, // white 白
  {CLUT4_COLOR_FULL_TRANS, CLUT4_COLOR_STROKE_RED,   CLUT4_COLOR_MIDDLE_RED,   CLUT4_COLOR_FULL_RED}, // red 红
  {CLUT4_COLOR_FULL_TRANS, CLUT4_COLOR_STROKE_BLUE,  CLUT4_COLOR_MIDDLE_BLUE,  CLUT4_COLOR_FULL_BLUE}, // blue 蓝
  {CLUT4_COLOR_FULL_TRANS, CLUT4_COLOR_STROKE_GREEN, CLUT4_COLOR_MIDDLE_GREEN, CLUT4_COLOR_FULL_GREEN}  // green 绿
};
#endif

static void FONT_Convert_Y(FONT_DATA_BUFFER *pCacheBuffer,
                           int nOffset,
                           unsigned char *pYBuffer,
                           int Stride,
                           int offset,
                           unsigned char color) {
  unsigned char  *psrc;
  unsigned char *pdst;
  psrc = (unsigned char *)pCacheBuffer->pdatabuf;
  pdst = pYBuffer + offset + nOffset * 2;
  for (int j = 0; j < pCacheBuffer->height; j++) {
    for (int i = 0, k = 0; i < (pCacheBuffer->width + 3) >> 2; i++) {
      if (!psrc[i]) {
        k += 4;
        continue;
      }
      if ((psrc[i] & 0x03) == 3) {
        pdst[k] = color;
      } else if ((psrc[i] & 0x03) > 0) {
        pdst[k] = 0x0000;
      }
      if (((psrc[i] >> 2) & 0x03) == 3) {
        pdst[k + 1] = color;
      } else if (((psrc[i] >> 2) & 0x03) > 0) {
        pdst[k + 1] = 0x0000;
      }
      if (((psrc[i] >> 4) & 0x03) == 3) {
        pdst[k + 2] = color;
      } else if (((psrc[i] >> 4) & 0x03) > 0) {
        pdst[k + 2] = 0x0000;
      }
      if (((psrc[i] >> 6) & 0x03) == 3) {
        pdst[k + 3] = color;
      } else if (((psrc[i] >> 6) & 0x03) > 0) {
        pdst[k + 3] = 0x0000;
      }
      k += 4;
    }
    pdst += Stride;
    psrc += pCacheBuffer->step >> 2;
  }
}

static void FONT_Convert_UV(FONT_DATA_BUFFER *pCacheBuffer,
                            int nOffset,
                            unsigned char *pUBuffer,
                            unsigned char *pVBuffer,
                            int Stride,
                            int offset,
                            unsigned char ucolor,
                            unsigned char vcolor) {
  unsigned char *psrc   = pCacheBuffer->pdatabuf;
  unsigned char *pdst_u = pUBuffer + offset + nOffset * 2;
  unsigned char *pdst_v = pVBuffer + offset + nOffset * 2;
  for (int j = 0; j < pCacheBuffer->height;) {
    for (int i = 0, k = 0; i < ((pCacheBuffer->width + 3) >> 2 ); i++) {
      if (!psrc[i]) {
        k += 2;
        continue;
      }
      if ((psrc[i] & 0x03) == 3) {
        pdst_u[k] = ucolor;
        pdst_u[k + 1] = ucolor;
        pdst_v[k] = vcolor;
        pdst_v[k + 1] = vcolor;
      }
      k += 2;
    }
    j += 2;
    pdst_u += Stride;
    pdst_v += Stride;
    psrc += pCacheBuffer->step >> 1;
  }
}

#ifdef ENABLE_CUSTOM_OSD_COLOR
uint32_t g_clut4[16] = {
  0x00000000, // full trans
  0xffffffff, // full white
  0xffb84d47, // red
  0xff2423dc, // blue
  0xff7ab847, // green
  0xff000000, // full black
  0xffb3b41e,
  0xffdc237e,
  0xff8e9f60,
  0xff23dc81,
  0xff47b2b8,
  0xffdc237e,
  0xff71609f,
  0xff609f91,
  0xff8547b8,
  0xff9f606e,
};
#else
uint32_t g_clut4[16] = {
  0x00000000, // full trans
  0xffffffff, // full white
  0xffff0000, // full red
  0xff0000ff, // full blue
  0xff00ff00, // full green
  0xff000000, // full black
  0xff080808, // stroke white
  0xff080000, // stroke red
  0xff000008, // stroke blue
  0xff000800, // stroke green
  0xff838383, // middle white
  0xff830000, // middle red
  0xff000083, // middle blue
  0xff008300, // middle green
  0xffffff00, // full yellow
  0xffff00ff, // full magenta
};
#endif

static void FONT_Convert_CLUT4(FONT_DATA_BUFFER *pCacheBuffer,
                                 const int nOffset,
                                 unsigned short* pRGNBuffer,
                                 int RGBStride,
                                 int color) {

  if (nOffset + (pCacheBuffer->step >> 2) > RGBStride) {
    printf("Multi_CLUT4 nOffset: %d, RGBStride: %d\n", nOffset, RGBStride);
    return;
  }
  unsigned char  *psrc;
  unsigned short *pdst;
  psrc = (unsigned char *)pCacheBuffer->pdatabuf;
  pdst = pRGNBuffer + nOffset;
  // printf("size: [%d, %d], offset: %d, pdst: %p, RGNBuffer: %p\n", pCacheBuffer->width, pCacheBuffer->height, nOffset, pdst, pRGNBuffer);
  for (int j = 0; j < pCacheBuffer->height; j++) {
    for (int i = 0, k = 0; i < pCacheBuffer->step >> 2; i++, k++) { // 1 short contain 4 pixel
      // printf("i: %d, j: %d, k: %d\n", i, j, k);
      // printf("%p, %p, %p, %p\n", &pdst[k], &pdst[k+1], &pdst[k+2], &pdst[k+3]);
      // 4 pixel
      unsigned char pixel = psrc[i];
      pdst[k] = I2GrayToClut4[color][pixel & 0x03] |
                  I2GrayToClut4[color][(pixel >> 2) & 0x03] << 4 |
                  I2GrayToClut4[color][(pixel >> 4) & 0x03] << 8 |
                  I2GrayToClut4[color][(pixel >> 6) & 0x03] << 12;
    }
    pdst += RGBStride; // canvas换行
    psrc += pCacheBuffer->step >> 2; // char buffer换行
  }
}

static void FONT_Convert_Multi_CLUT4(FONT_DATA_BUFFER *pCacheBuffer, const int nWidthOffset, const int nHeightOffset,
                                       int nFontColor, unsigned short* pRGNBuffer, int RGBStride) {
  unsigned char  *psrc;
  unsigned short *pdst;
  if (nFontColor < 0 || nFontColor >= MAX_FONT_COLOR_TYPE) {
    return;
  }
  if (nWidthOffset + (pCacheBuffer->step >> 2) > RGBStride) {
    printf("Multi_CLUT4 nWidthOffset: %d, RGBStride: %d\n", nWidthOffset, RGBStride);
    return;
  }
  psrc = (unsigned char *)pCacheBuffer->pdatabuf;
  pdst = pRGNBuffer + nWidthOffset + nHeightOffset * RGBStride;
  for (int j = 0; j < pCacheBuffer->height; j++) {
    for (int i = 0, k = 0; i < pCacheBuffer->step >> 2; i++, k++) {
      unsigned char pixel = psrc[i];
      pdst[k] = I2GrayToClut4[nFontColor][pixel & 0x03] |
                  I2GrayToClut4[nFontColor][(pixel >> 2) & 0x03] << 4 |
                  I2GrayToClut4[nFontColor][(pixel >> 4) & 0x03] << 8 |
                  I2GrayToClut4[nFontColor][(pixel >> 6) & 0x03] << 12;
    }
    pdst += RGBStride;
    psrc += pCacheBuffer->step >> 2;
  }
}

static void FONT_Convert_RGB1555(FONT_DATA_BUFFER *pCacheBuffer,
                                 const int nOffset,
                                 unsigned short* pRGNBuffer,
                                 int RGBStride,
                                 int color) {
  // 再做道保险，防止越界; 宽的偏移量 + 要操作的字符像素，不能超过RGBStride
  if (nOffset + ALIGN_UP(pCacheBuffer->width, 4) > RGBStride) {
    printf("RGB1555 nOffset: %d, RGBStride: %d\n", nOffset, RGBStride);
    return;
  }
  unsigned char  *psrc;
  unsigned short *pdst;
  psrc = (unsigned char *)pCacheBuffer->pdatabuf;
  pdst = pRGNBuffer + nOffset;
  // printf("size: [%d, %d], offset: %d, pdst: %p, RGNBuffer: %p\n", pCacheBuffer->width, pCacheBuffer->height, nOffset, pdst, pRGNBuffer);
  for (int j = 0; j < pCacheBuffer->height; j++) {
    for (int i = 0, k = 0; i < (pCacheBuffer->width + 3) >> 2; i++) {
      // printf("i: %d, j: %d, k: %d\n", i, j, k);
      // printf("%p, %p, %p, %p\n", &pdst[k], &pdst[k+1], &pdst[k+2], &pdst[k+3]);
      // 4 pixel
      unsigned char pixel = psrc[i];
      pdst[k] = I2GrayToRGB1555Lut[color][pixel & 0x03];
      pdst[k + 1] = I2GrayToRGB1555Lut[color][(pixel >> 2) & 0x03];
      pdst[k + 2] = I2GrayToRGB1555Lut[color][(pixel >> 4) & 0x03];
      pdst[k + 3] = I2GrayToRGB1555Lut[color][(pixel >> 6) & 0x03];
      k += 4;
    }
    pdst += RGBStride;
    psrc += pCacheBuffer->step >> 2;
  }
}

static void FONT_Convert_Multi_RGB1555(FONT_DATA_BUFFER *pCacheBuffer, const int nWidthOffset, const int nHeightOffset,
                                       int nFontColor, unsigned short* pRGNBuffer, int RGBStride) {
  unsigned char  *psrc;
  unsigned short *pdst;
  if (nFontColor < 0 || nFontColor > MAX_FONT_COLOR_TYPE) {
    return;
  }
  // 再做道保险，防止越界; 宽的偏移量 + 要操作的字符像素，不能超过RGBStride
  if (nWidthOffset + ALIGN_UP(pCacheBuffer->width, 4) > RGBStride) {
    printf("Multi_RGB1555 nWidthOffset: %d, RGBStride: %d\n", nWidthOffset, RGBStride);
    return;
  }
  psrc = (unsigned char *)pCacheBuffer->pdatabuf;
  pdst = pRGNBuffer + nWidthOffset + nHeightOffset * RGBStride;
  for (int j = 0; j < pCacheBuffer->height; j++) {
    for (int i = 0, k = 0; i < (pCacheBuffer->width + 3) >> 2; i++) {
      unsigned char pixel = psrc[i];
      pdst[k] = I2GrayToRGB1555Lut[nFontColor][pixel & 0x03];
      pdst[k + 1] = I2GrayToRGB1555Lut[nFontColor][(pixel >> 2) & 0x03];
      pdst[k + 2] = I2GrayToRGB1555Lut[nFontColor][(pixel >> 4) & 0x03];
      pdst[k + 3] = I2GrayToRGB1555Lut[nFontColor][(pixel >> 6) & 0x03];
      k += 4;
    }
    pdst += RGBStride;
    psrc += pCacheBuffer->step >> 2;
  }
}

FONT_DATA_BUFFER* Font_GetCacheBmp(VzCharTable *pTable, unsigned int ucKey, int fontsize) {
  FONT_DATA_BUFFER *pData = NULL;

  if (pTable->font_data.count(fontsize) == 0) {
    printf("not found font size: %d\n", fontsize);
    return NULL;
  }

  if (ucKey > 0x80) {
    pData = pTable->font_data[fontsize][FONT_CHAR_GB2312_PLATE]->FontData_GetCacheBmp(ucKey);
    if (!pData) {
      return pTable->font_data[fontsize][FONT_CHAR_GB2312_OTHER]->FontData_GetCacheBmp(ucKey);
    }
  } else {
    pData = pTable->font_data[fontsize][FONT_CHAR_ASCII]->FontData_GetCacheBmp(ucKey);
    if (!pData) {
      return pTable->font_data[fontsize][FONT_CHAR_ASCII_OTHER]->FontData_GetCacheBmp(ucKey);
    }
  }
  return pData;
}

int LoadCustomClutFromFile(const char *path) {
  // ASSERT_RETURN_FAILURE(path == nullptr, -1);

  std::ifstream ifs(path);
  if (!ifs) {
    printf("load custom color table failed, %s not found\n", path);
    return -1;
  } else {
    uint16_t line = 0;
    do {
      if (line >= (sizeof(g_clut4) / sizeof(g_clut4[0]))) break;
      char str[16];
      uint32_t val = 0;
      ifs.getline(str, sizeof(str));
      sscanf(str, "#%8x", &val);
      g_clut4[line] = val;
      line++;
    } while(!ifs.eof());
    // Log_HexDump("custom-clut4", (uint8_t*)g_clut4, sizeof(g_clut4));
  }

  return 0;
}

VzCharTable *VzCharTab_Create(uint32_t max_font_size) {
  VzCharTable *pTable = new VzCharTable();

#ifdef OPTIMIZE_FONT_MEM_SIZE
  g_font_size = {16, 24, 32, 48, 64, 96, 128};
#else
  g_font_size = {4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 28, 30, 32, 38, 42, 48, 60, 64, 96, 128};
#endif
  g_font_size.remove_if([&](uint32_t n) { return n > max_font_size;});

  for (auto size : g_font_size) {
    for (auto type : g_font_char_type) {
      if (type == FONT_CHAR_ASCII) {
        pTable->font_data[size][type] = new FontData(size, sizeof(PLATE_ASC_LIST), PLATE_ASC_LIST, 0);
      } else if (type == FONT_CHAR_ASCII_OTHER) {
        pTable->font_data[size][type] = new FontData(size, ASC_OTHER_CACHE_NUM, "", 1);
      } else if (type == FONT_CHAR_GB2312_PLATE) {
        pTable->font_data[size][type] = new FontData(size, sizeof(PLATE_GB2312_LIST) * 2, (char*)PLATE_GB2312_LIST, 0);
      } else if (type == FONT_CHAR_GB2312_OTHER) {
        pTable->font_data[size][type] = new FontData(size, GB2312_OTHER_CACHE_NUM, "", 1);
      }
    }
  }

  //printf("crate font cache complete....\n");
  return pTable;
}

std::list<uint32_t> &VzGetAllFontSize() {
  return g_font_size;
}

void VzCharTab_Destroy(VzCharTable *tab) {
  //printf("destroy font cache\n");
  if (tab == NULL) {
    return;
  }
  for (auto size : g_font_size) {
    for (auto type : g_font_char_type) {
        delete tab->font_data[size][type];
    }
  }

  tab->font_data.clear();

  delete tab;
  tab = NULL;
}

int VzCharTab_GetUsedMemorySize(VzCharTable *tab) {
  std::lock_guard<std::mutex> font_lock(g_font_mutex_);
  int size = 0;
  if (tab == NULL) {
    return 0;
  }

  for (auto font_size : g_font_size) {
    for (auto type : g_font_char_type) {
      size += tab->font_data[font_size][type]->GetUsedMemorySize();
    }
  }

  return size;
}

int GetStringSize(VzCharTable *pTable, const std::string &str, int font_size, int &width, int &height) {
  unsigned char* pOsdStringTemp = (unsigned char *)str.c_str();
  int nOsdStringlen = str.size();
  unsigned int wkey;
  int w = 0, h = 0;

  if (pTable == NULL) {
    width = 0;
    height = 0;
    return -1;
  }

  int stepBytes = 0;
  //对OSD字符进行轮询计算区域缓存大小
  while (nOsdStringlen > 0) {
    stepBytes = ToUnicodeKey(pOsdStringTemp,nOsdStringlen,wkey);
    if (stepBytes <= 0) {
      printf("Convert to Unicode Key Failed\n");
      return -1;
    }
    FONT_DATA_BUFFER *bmpbuffer = Font_GetCacheBmp(pTable, wkey, font_size);
    if (bmpbuffer && bmpbuffer->pdatabuf != NULL) {
      // ARGB_1555和ARGB_CLUT4每次操作4个像素，宽度4对齐，保证bmp_buf操作不越界
      w += ALIGN_UP(bmpbuffer->width, 4);
      h = bmpbuffer->height > h ? bmpbuffer->height : h;
    }
    nOsdStringlen -= stepBytes;
    pOsdStringTemp += stepBytes;
  }
  width = ALIGN_UP(w, 2);
  height = ALIGN_UP(h, 2);

  return 0;
}

int VzCharTab_Get_Char_Width(VzCharTable *tab, const int nFontSize, const int nFontSpacing, const char *pOsdString, int &width, int &height) {
  std::lock_guard<std::mutex> font_lock(g_font_mutex_);
  return GetStringSize(tab, pOsdString, nFontSize, width, height);
}

//
bool VzCharTab_GetBmp_YUV(VzCharTable *tab,
                          int nFontSize,
                          const int nFontSpacing,
                          const char *pOsdString,
                          unsigned char *pYBuffer,
                          unsigned char *pUBuffer,
                          unsigned char *pVBuffer,
                          int stride,
                          int yoffset,
                          int uvoffset,
                          unsigned char ycolor,
                          unsigned char ucolor,
                          unsigned char vcolor) {
  if (!tab) {
    printf("tab should not be NULL !!!\n");
    return false;
  }
  if (!pOsdString) {
    printf("Invalid string, should not be NULL !!!\n");
    return false;
  }
  if (!pYBuffer || !pUBuffer || !pVBuffer) {
    printf("Invalid buffer, should not be NULL !!!\n");
    return false;
  }
  unsigned char *pOsdStringTemp = (unsigned char *)pOsdString;
  int nOsdStringlen = strlen(pOsdString);
  int nOffset = 0;
  unsigned int wkey;
  int stepBytes = 0;

  while (nOsdStringlen > 0) {
    stepBytes = ToUnicodeKey(pOsdStringTemp, nOsdStringlen, wkey);
    if (stepBytes <= 0) {
      printf("Convert to Unicode Key Failed\n");
      return false;
    }
    FONT_DATA_BUFFER *bmpbuffer = Font_GetCacheBmp(tab, wkey, nFontSize);
    if (((nOffset >> 1) + bmpbuffer->width) > stride) {
      return true;
    }
    if (bmpbuffer != NULL && bmpbuffer->pdatabuf != NULL) {
      FONT_Convert_Y(bmpbuffer, nOffset >> 1, pYBuffer,
                     stride, yoffset, ycolor);
      FONT_Convert_UV(bmpbuffer, nOffset >> 2, pUBuffer, pVBuffer,
                      stride >> 1, uvoffset, ucolor, vcolor);
      nOffset += bmpbuffer->width;
    }

    pOsdStringTemp += stepBytes;
    nOsdStringlen -= stepBytes;
  }
  return true;
}

//
bool VzCharTab_GetBmp(VzCharTable *tab,
                      int nFontSize,
                      const int nFontSpacing,
                      const char *pOsdString,
                      unsigned short *pRGB_Buffer,
                      int RGBStride,
                      int color, CanvasPixelFormat canvas_pix_fmt) {
  std::lock_guard<std::mutex> font_lock(g_font_mutex_);
  unsigned char * pOsdStringTemp = (unsigned char *)pOsdString;
  unsigned short *pRGBBufferTemp = pRGB_Buffer;
  int nOffset = 0;
  unsigned int wkey;
  int stepBytes = 0;

  if (tab == NULL) {
    return false;
  }

  if(pOsdString == NULL) {
    printf("Invalid string, should not be NULL !!!\n");
    return false;
  }
  if(pRGB_Buffer == NULL) {
    printf("Invalid buffer, should not be NULL !!!\n");
    return false;
  }
  if (canvas_pix_fmt != CANVAS_PIXEL_FORMAT_ARGB_1555 && canvas_pix_fmt != CANVAS_PIXEL_FORMAT_ARGB_CLUT4) {
    printf("unsupported fmt: %d\n", canvas_pix_fmt);
    return false;
  }
  int nOsdStringlen = strlen(pOsdString);

  while (nOsdStringlen > 0) {
    stepBytes = ToUnicodeKey(pOsdStringTemp,nOsdStringlen,wkey);
    if (stepBytes <= 0) {
      printf("Convert to Unicode Key Failed\n");
      return false;
    }
    FONT_DATA_BUFFER *bmpbuffer = Font_GetCacheBmp(tab, wkey, nFontSize);
    if (bmpbuffer != NULL && bmpbuffer->pdatabuf != NULL) {
      if ((nOffset + bmpbuffer->width) > RGBStride) {
        printf("warning,reach the width boarder,offset is %d,the remain information will be truncated\n", nOffset);
        return true;
      }
      if (canvas_pix_fmt == CANVAS_PIXEL_FORMAT_ARGB_1555) {
        FONT_Convert_RGB1555(bmpbuffer, nOffset, pRGBBufferTemp, RGBStride, color);
      } else if (canvas_pix_fmt == CANVAS_PIXEL_FORMAT_ARGB_CLUT4) {
        FONT_Convert_CLUT4(bmpbuffer, nOffset / 4, pRGBBufferTemp, RGBStride / 4, color);
      }
      // nOffset += ALIGN_UP(bmpbuffer->width, 4);
      nOffset += bmpbuffer->offset;
      printf("----------%04x, nOffset: %d, adv.x: %d\n", wkey, nOffset, bmpbuffer->width);
    } else {
      // printf("==== get bmp for key %u failed\n",wkey);
    }

    pOsdStringTemp += stepBytes;
    nOsdStringlen -= stepBytes;
  }
#if 0
  printf("VzCharTab_GetUsedMemorySize:%d\n", VzCharTab_GetUsedMemorySize(tab));
#endif
  return true;
}


bool VzCharTab_GetMultiBmp(VzCharTable *tab, int nFontSize[], const int nFontSpacing,
                           char **pOsdString, int nFontColor[], int number,
                           unsigned short *pRGB_Buffer, int RGBStride, int MaxWidth, int Align, CanvasPixelFormat canvas_pix_fmt) {
  std::lock_guard<std::mutex> font_lock(g_font_mutex_);
  if (tab == NULL) {
    return false;
  }
  if(pOsdString == NULL) {
    printf("Invalid string, should not be NULL !!!\n");
    return false;
  }
  if(pRGB_Buffer == NULL) {
    printf("Invalid buffer, should not be NULL !!!\n");
    return false;
  }
  if (canvas_pix_fmt != CANVAS_PIXEL_FORMAT_ARGB_1555 && canvas_pix_fmt != CANVAS_PIXEL_FORMAT_ARGB_CLUT4) {
    printf("unsupported fmt: %d\n", canvas_pix_fmt);
    return false;
  }
  int nHeightOffset = 0;
  for (int i = 0; i < number; i++) {
    unsigned char* pOsdStringTemp = (unsigned char *)pOsdString[i];
    unsigned short *pRGBBufferTemp = pRGB_Buffer;
    int nOsdStringlen = strlen((const char *)pOsdStringTemp);
    int nOffset = 0;
    unsigned int wkey;
    int maxhightoffset=0;
    int stepBytes = 0;

    //printf("OSD Get %d %d\n", nFontSize[i], nFontColor[i]);
    if (Align == MULTI_LINE_ALIGN_RIGHT) {
      int string_width = MaxWidth;
      int string_height = 0;
      GetStringSize(tab, pOsdString[i], nFontSize[i], string_width, string_height);
      if (canvas_pix_fmt == CANVAS_PIXEL_FORMAT_ARGB_1555) {
        pRGBBufferTemp += MaxWidth - string_width;
      } else if (canvas_pix_fmt == CANVAS_PIXEL_FORMAT_ARGB_CLUT4) {
        pRGBBufferTemp += (MaxWidth - string_width) / 4;
      }
    }

    while (nOsdStringlen > 0) {
      stepBytes = ToUnicodeKey(pOsdStringTemp,nOsdStringlen, wkey);
      if (stepBytes <= 0) {
        printf("Convert to Unicode Key Failed\n");
        break;
      }
      FONT_DATA_BUFFER *bmpbuffer = Font_GetCacheBmp(tab, wkey, nFontSize[i]);
      if (bmpbuffer != NULL && bmpbuffer->pdatabuf != NULL) {
        if ((nOffset + bmpbuffer->width) > RGBStride) {
          //JMSG("warning,reach the width boarder,offset is %d,the remain information will be truncated", nOffset);
          break;
        }
        if (canvas_pix_fmt == CANVAS_PIXEL_FORMAT_ARGB_1555) {
          FONT_Convert_Multi_RGB1555(bmpbuffer, nOffset, nHeightOffset,nFontColor[i],pRGBBufferTemp, RGBStride);
        } else if (canvas_pix_fmt == CANVAS_PIXEL_FORMAT_ARGB_CLUT4) {
          FONT_Convert_Multi_CLUT4(bmpbuffer, nOffset / 4, nHeightOffset, nFontColor[i], pRGBBufferTemp, RGBStride / 4);
        }
        nOffset += ALIGN_UP(bmpbuffer->width, 4);
        maxhightoffset = maxhightoffset > bmpbuffer->height ? maxhightoffset : bmpbuffer->height;
      }
      pOsdStringTemp += stepBytes;
      nOsdStringlen -= stepBytes;
    }
    nHeightOffset += maxhightoffset;
  }
  return true;
}

bool VzCharTab_GetMultiBmp(VzCharTable *tab, std::vector<int> &nFontSize, const int nFontSpacing,
                           std::vector<std::string> &pOsdString,  std::vector<int> &nFontColor, int number,
                           unsigned short *pRGB_Buffer, int RGBStride, int MaxWidth, int Align, CanvasPixelFormat canvas_pix_fmt) {
  std::lock_guard<std::mutex> font_lock(g_font_mutex_);
  if (tab == NULL) {
    return false;
  }
  if(pOsdString.empty()) {
    printf("Invalid string, should not be NULL !!!\n");
    return false;
  }
  if(pRGB_Buffer == NULL) {
    printf("Invalid buffer, should not be NULL !!!\n");
    return false;
  }
  if (canvas_pix_fmt != CANVAS_PIXEL_FORMAT_ARGB_1555 && canvas_pix_fmt != CANVAS_PIXEL_FORMAT_ARGB_CLUT4) {
    printf("unsupported fmt: %d\n", canvas_pix_fmt);
    return false;
  }
  int nHeightOffset = 0;
  for (int i = 0; i < number; i++) {
    unsigned char *pOsdStringTemp = (unsigned char *)(pOsdString[i].c_str());
    unsigned short *pRGBBufferTemp = pRGB_Buffer;
    int nOsdStringlen = pOsdString[i].size();
    int nOffset = 0;
    unsigned int wkey;
    int maxhightoffset = 0;
    int stepBytes = 0;

    //printf("OSD Get %d %d\n", nFontSize[i], nFontColor[i]);
    if (Align == MULTI_LINE_ALIGN_RIGHT) {
      int string_width = MaxWidth;
      int string_height = 0;
      GetStringSize(tab, pOsdString[i], nFontSize[i], string_width, string_height);
      //printf("pOsdString:%s, string_width:%d, string_height:%d, MaxWidth:%d\n", pOsdString[i].c_str(), string_width, string_height, MaxWidth);
      if (string_width > MaxWidth) {
        string_width = MaxWidth;
      }
      if (canvas_pix_fmt == CANVAS_PIXEL_FORMAT_ARGB_1555) {
        pRGBBufferTemp += MaxWidth - string_width;
      } else if (canvas_pix_fmt == CANVAS_PIXEL_FORMAT_ARGB_CLUT4) {
        pRGBBufferTemp += (MaxWidth - string_width) / 4;
      }
    }
    while (nOsdStringlen > 0) {
      stepBytes = ToUnicodeKey(pOsdStringTemp, nOsdStringlen, wkey);
      if (stepBytes <= 0) {
        printf("Convert to Unicode Key Failed\n");
        break;
      }
      FONT_DATA_BUFFER *bmpbuffer = Font_GetCacheBmp(tab, wkey, nFontSize[i]);
      if (bmpbuffer != NULL && bmpbuffer->pdatabuf != NULL) {
        if ((nOffset + bmpbuffer->width) > RGBStride) {
          //JMSG("warning,reach the width boarder,offset is %d,the remain information will be truncated", nOffset);
          break;
        }
        if (canvas_pix_fmt == CANVAS_PIXEL_FORMAT_ARGB_1555) {
          FONT_Convert_Multi_RGB1555(bmpbuffer, nOffset, nHeightOffset, nFontColor[i], pRGBBufferTemp, RGBStride);
        } else if (canvas_pix_fmt == CANVAS_PIXEL_FORMAT_ARGB_CLUT4) {
          FONT_Convert_Multi_CLUT4(bmpbuffer, nOffset / 4, nHeightOffset, nFontColor[i], pRGBBufferTemp, RGBStride / 4);
        }
        nOffset +=  ALIGN_UP(bmpbuffer->width, 4);
        maxhightoffset = maxhightoffset > bmpbuffer->height ? maxhightoffset : bmpbuffer->height;
      }
      pOsdStringTemp += stepBytes;
      nOsdStringlen -= stepBytes;
    }
    nHeightOffset += maxhightoffset;
  }
  return true;
}

FontData::FontData(int fontsize, int maxsize, const char *str, int brefresh) {
  if (maxsize <= 0 || fontsize <= 0)
    return;
  //printf("Font data enter\n");
  pdatabuffer = (FONT_DATA_BUFFER *)calloc(maxsize, sizeof(FONT_DATA_BUFFER));
  pfontindex = (unsigned int*)calloc(maxsize, sizeof(unsigned int));
  m_fontsize = fontsize;
  m_maxsize = maxsize;
  m_brefresh = brefresh;
  m_blackbordersize = m_fontsize >> 4;
  m_Cache_Index = 0;
  m_Cache_ReIndex = 0;

  // unsigned int wkey;
  // unsigned int wc;
  // for (int i = 0; i < strlen(str); ) {
  //   //printf("process font data %d\n",i);
  //   if (str[i] > 0x80) {
  //     wkey = GB2312_Get_Font_Index((const unsigned char*)str + i);
  //     GBcode_Unicode((unsigned char*)&wc, (unsigned char*)&wkey);
  //     wc = ((wc & 0xff) << 8) + (wc >> 8);
  //     ftGetCharBitMap(m_fontsize, m_blackbordersize, wc, &pdatabuffer[m_Cache_Index]);
  //     pfontindex[m_Cache_Index] = wc;
  //     i += 2;
  //   } else {
  //     ftGetCharBitMap(m_fontsize, m_blackbordersize, str[i], &pdatabuffer[m_Cache_Index]);
  //     pfontindex[m_Cache_Index] = str[i];
  //     i += 1;
  //   }
  //   m_Cache_Index++;
  //   if (m_Cache_Index >= m_maxsize)
  //     break;
  // }
}



FontData::~FontData() {
  for (int i = 0; i < m_maxsize; i++) {
    if (&pdatabuffer[i] != NULL) {
      if (pdatabuffer[i].pdatabuf != NULL) {
        free(pdatabuffer[i].pdatabuf);
        pdatabuffer[i].pdatabuf = NULL;
      }
    }
  }
  if (pdatabuffer != NULL) {
    free(pdatabuffer);
    pdatabuffer = NULL;
  }

  if (pfontindex != NULL) {
    free(pfontindex);
    pfontindex = NULL;
  }
  m_Cache_Index = 0;
  m_Cache_ReIndex = 0;
}


FONT_DATA_BUFFER * FontData::FontData_GetCacheBmp(unsigned int ucKey) {

  for (int i = 0; i < m_Cache_Index; i++) {
    if (pfontindex[i] == ucKey) {
      return &pdatabuffer[i];
    }
  }

  if (!m_brefresh && m_Cache_Index >= m_maxsize)
    return NULL;

  unsigned int cacheindex = 0;
  if (m_Cache_Index >= m_maxsize) {
    cacheindex = m_Cache_ReIndex;
    m_Cache_ReIndex ++;
    m_Cache_ReIndex = m_Cache_ReIndex >= m_maxsize ? 0 : m_Cache_ReIndex;
  } else {
    cacheindex = m_Cache_Index;
    m_Cache_Index ++;
  }
  ftGetCharBitMap(m_fontsize, m_blackbordersize, ucKey, &pdatabuffer[cacheindex]);
  pfontindex[cacheindex] = ucKey;
  return &pdatabuffer[cacheindex];
}

int FontData::GetUsedMemorySize() {
  int size = 0;
  size += m_maxsize * sizeof(FONT_DATA_BUFFER*);
  size += m_maxsize * sizeof(FONT_DATA_BUFFER);
  size += m_maxsize * sizeof(unsigned short);
  for (int i = 0; i < m_Cache_Index; i++) {
    if(&pdatabuffer[i] && pdatabuffer[i].pdatabuf)
      size += (pdatabuffer[i].step * pdatabuffer[i].height) >> 2;
  }
  return size;
}
}
