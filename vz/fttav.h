#ifndef FTTAV_H_
#define FTTAV_H_


#include <stdio.h>
#include "chartab.h"

namespace SAL {

typedef int ft_bool;
#define ft_false 0
#define ft_true 1

#ifndef JMSGS
#define JMSGS
//#define MSG(func, line, message, ...) \
//  fprintf(stderr, "file :"#func" line: "#line" :"message"\n", ##__VA_ARGS__)
//
//#define MSGEXP(func, line, message, ...) \
//  MSG(func, line, message, ##__VA_ARGS__)
//
//#define JMSG(message, ...) \
//  MSGEXP(__FILE__, __LINE__, message, ##__VA_ARGS__)
//#endif

#define JMSG(message,...)\
		printf("[%s %d] -> ",__FUNCTION__,__LINE__);\
		printf(message,##__VA_ARGS__);\
		printf("\r\n");
#endif

ft_bool ftInit(const char *filename);
void    ftFree();
int     ftGetCharBitMap(int fontsize, int blackbordersize, unsigned int wkey, FONT_DATA_BUFFER *pdatabuffer);
}
#endif
