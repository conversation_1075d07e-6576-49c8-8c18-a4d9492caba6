#ifndef _VZ_CHAR_TAB_H_
#define _VZ_CHAR_TAB_H_

#include <map>
#include <vector>
#include <string>
#include <list>
#include <mutex>
// #include "sal/media/inc/vz_types.h"

#ifdef CHIP_PLATFORM_HISI_V6_0
#define CANVAS_CLUT4_FMT_USED 1
#endif

namespace SAL {

#ifndef ALIGN_UP
#define ALIGN_UP(x, a)           ( ( ((x) + ((a) - 1) ) / a ) * a )
#endif

#ifndef ALIGN_DOWN
#define ALIGN_DOWN(x, a)         ( ( (x) / (a)) * (a) )
#endif

enum CanvasPixelFormat {
  CANVAS_PIXEL_FORMAT_ARGB_1555 = 0,
  CANVAS_PIXEL_FORMAT_ARGB_4444,
  CANVAS_PIXEL_FORMAT_ARGB_8565,
  CANVAS_PIXEL_FORMAT_ARGB_8888,
  CANVAS_PIXEL_FORMAT_ARGB_2BPP,
  CANVAS_PIXEL_FORMAT_ARGB_CLUT2,
  CANVAS_PIXEL_FORMAT_ARGB_CLUT4,
  CANVAS_PIXEL_FORMAT_RGB_444,
  CANVAS_PIXEL_FORMAT_RGB_565,
  CANVAS_PIXEL_FORMAT_RGB_888,
  CANVAS_PIXEL_FORMAT_BUTT
};

enum CLUT4Index {
  CLUT4_COLOR_FULL_TRANS = 0x0,
  CLUT4_COLOR_FULL_WHITE = 0x1,
  CLUT4_COLOR_FULL_RED = 0x2,
  CLUT4_COLOR_FULL_BLUE = 0x3,
  CLUT4_COLOR_FULL_GREEN = 0x4,
  CLUT4_COLOR_FULL_BLACK = 0x5,

  CLUT4_COLOR_STROKE_WHITE = 0x6,
  CLUT4_COLOR_STROKE_RED = 0x7,
  CLUT4_COLOR_STROKE_BLUE = 0x8,
  CLUT4_COLOR_STROKE_GREEN = 0x9,
  CLUT4_COLOR_MIDDLE_WHITE = 0xa,
  CLUT4_COLOR_MIDDLE_RED = 0xb,
  CLUT4_COLOR_MIDDLE_BLUE = 0xc,
  CLUT4_COLOR_MIDDLE_GREEN = 0xd,
  CLUT4_COLOR_FULL_YELLOW = 0xe,
  CLUT4_COLOR_FULL_MAGENTA = 0xf,

  CLUT4_COLOR_CUSTOM_0 = 0x6,
  CLUT4_COLOR_CUSTOM_1 = 0x7,
  CLUT4_COLOR_CUSTOM_2 = 0x8,
  CLUT4_COLOR_CUSTOM_3 = 0x9,
  CLUT4_COLOR_CUSTOM_4 = 0xa,
  CLUT4_COLOR_CUSTOM_5 = 0xb,
  CLUT4_COLOR_CUSTOM_6 = 0xc,
  CLUT4_COLOR_CUSTOM_7 = 0xd,
  CLUT4_COLOR_CUSTOM_8 = 0xe,
  CLUT4_COLOR_CUSTOM_9 = 0xf,

  CLUT4_COLOR_BUTT
};

extern uint32_t g_clut4[16];

typedef struct {
  unsigned char *pdatabuf;
  int width;
  int height;
  int step;
  int offset;
} FONT_DATA_BUFFER;

class FontData {
 public:
  FontData(int fontsize, int maxsize, const char *str, int brefresh);
  ~FontData();

  FONT_DATA_BUFFER *FontData_GetCacheBmp(unsigned int ucKey);
  int GetUsedMemorySize();

 private:
  FONT_DATA_BUFFER *pdatabuffer;
  unsigned int *pfontindex;
  int m_maxsize;
  int m_fontsize;
  int m_blackbordersize;
  int m_brefresh;
  unsigned int m_Cache_Index, m_Cache_ReIndex;
};

enum FontCharType {
  FONT_CHAR_ASCII,
  FONT_CHAR_ASCII_OTHER,
  FONT_CHAR_GB2312_PLATE,
  FONT_CHAR_GB2312_OTHER,
};

enum MultiLineAlign {
  MULTI_LINE_ALIGN_LEFT = 0,
  MULTI_LINE_ALIGN_RIGHT = 1,
  MULTI_LINE_ALIGN_BUTT
};

typedef struct VzCharTable {
  std::map < uint32_t, std::map<FontCharType, FontData *>> font_data;
#if 0
  FontData *pASC_16_data;
  FontData *pASC_24_data;
  FontData *pASC_32_data;
  FontData *pASC_48_data;

  FontData *pGB2312_PLATE_16_Data;
  FontData *pGB2312_PLATE_24_Data;
  FontData *pGB2312_PLATE_32_Data;
  FontData *pGB2312_PLATE_48_Data;

  FontData *pASC_OTHER_16_Data;
  FontData *pASC_OTHER_24_Data;
  FontData *pASC_OTHER_32_Data;
  FontData *pASC_OTHER_48_Data;

  FontData *pGB2312_OTHER_16_Data;
  FontData *pGB2312_OTHER_24_Data;
  FontData *pGB2312_OTHER_32_Data;
  FontData *pGB2312_OTHER_48_Data;
#endif
} VzCharTable;
extern VzCharTable *g_char_tab_;
extern std::mutex g_chartab_mutex_;
VzCharTable *VzCharTab_Create(uint32_t max_font_size);
void VzCharTab_Destroy(VzCharTable *tab);
std::list<uint32_t> &VzGetAllFontSize();
int VzCharTab_Get_Char_Width(VzCharTable *tab, const int nFontSize, const int nFontSpacing,
                             const char *pOsdString, int &width, int &height);
bool VzCharTab_GetBmp_YUV(VzCharTable *tab, int nFontSize, const int nFontSpacing,
                          const char *pOsdString, unsigned char *pYBuffer,
                          unsigned char *pUBuffer, unsigned char *pVBuffer,
                          int stride, int yoffset, int uvoffset,
                          unsigned char ycolor, unsigned char ucolor,
                          unsigned char vcolor);
bool VzCharTab_GetBmp(VzCharTable *tab, int nFontSize, const int nFontSpacing,
                      const char *pOsdString, unsigned short *pRGB_Buffer, int RGBStride,
                      int color, CanvasPixelFormat canvas_pix_fmt = CANVAS_PIXEL_FORMAT_ARGB_1555);
bool VzCharTab_GetMultiBmp(VzCharTable *tab, int nFontSize[], const int nFontSpacing,
                           char **pOsdString, int nFontColor[], int number,
                           unsigned short *pRGB_Buffer, int RGBStride, int MaxWidth, int Align, CanvasPixelFormat canvas_pix_fmt = CANVAS_PIXEL_FORMAT_ARGB_1555);
bool VzCharTab_GetMultiBmp(VzCharTable *tab, std::vector<int> &nFontSize, const int nFontSpacing,
                           std::vector<std::string> &pOsdString, std::vector<int> &nFontColor, int number,
                           unsigned short *pRGB_Buffer, int RGBStride, int MaxWidth, int Align, CanvasPixelFormat canvas_pix_fmt = CANVAS_PIXEL_FORMAT_ARGB_1555);
int VzCharTab_GetUsedMemorySize(VzCharTable *tab);

int LoadCustomClutFromFile(const char *path);
}

#endif
