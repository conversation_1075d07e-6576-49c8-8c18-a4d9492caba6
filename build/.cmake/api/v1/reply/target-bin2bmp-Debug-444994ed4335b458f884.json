{"artifacts": [{"path": "bin2bmp"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "link_directories", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 57, "parent": 0}, {"command": 1, "file": 0, "line": 58, "parent": 0}, {"command": 2, "file": 0, "line": 12, "parent": 0}, {"command": 3, "file": 0, "line": 11, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g"}, {"fragment": "-std=gnu++14"}], "includes": [{"backtrace": 4, "path": "/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/include/freetype2"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "14"}, "sourceIndexes": [0, 1, 2, 3]}], "id": "bin2bmp::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "bin"}], "prefix": {"path": "/usr/local"}}, "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"backtrace": 3, "fragment": "-L/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/lib", "role": "libraryPath"}], "language": "CXX"}, "name": "bin2bmp", "nameOnDisk": "bin2bmp", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [4, 5, 6]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/bin2bmp_main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/bin2bmp.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/bitmap_converter.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/pixel_format.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/bin2bmp.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/bitmap_converter.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/pixel_format.h", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}