{"artifacts": [{"path": "vz_font"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "link_directories", "target_link_libraries", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 72, "parent": 0}, {"command": 1, "file": 0, "line": 75, "parent": 0}, {"command": 2, "file": 0, "line": 12, "parent": 0}, {"command": 3, "file": 0, "line": 73, "parent": 0}, {"command": 4, "file": 0, "line": 11, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g"}, {"fragment": "-std=gnu++14"}], "includes": [{"backtrace": 5, "path": "/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/include/freetype2"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "14"}, "sourceIndexes": [0, 1, 2, 3, 4, 6]}, {"compileCommandFragments": [{"fragment": "-g"}], "includes": [{"backtrace": 5, "path": "/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/include/freetype2"}], "language": "C", "sourceIndexes": [5]}], "id": "vz_font::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "bin"}], "prefix": {"path": "/usr/local"}}, "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"backtrace": 3, "fragment": "-L/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/lib", "role": "libraryPath"}, {"fragment": "-Wl,-rpath,/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/lib:", "role": "libraries"}, {"backtrace": 4, "fragment": "-lfreetype", "role": "libraries"}], "language": "CXX"}, "name": "vz_font", "nameOnDisk": "vz_font", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/bin2bmp.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/pixel_format.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "vz/chartab.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "vz/vzft.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "vz/char_code.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vz/char_code_c.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "vz/vz_font_test.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}