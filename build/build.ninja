# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: gen_font_bitmap
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /home/<USER>/workspace/projects/local/gen_font_bitmap/build/
# =============================================================================
# Object build statements for EXECUTABLE target gen_font_bitmap


#############################################
# Order-only phony target for gen_font_bitmap

build cmake_object_order_depends_target_gen_font_bitmap: phony || CMakeFiles/gen_font_bitmap.dir

build CMakeFiles/gen_font_bitmap.dir/src/main.cpp.o: CXX_COMPILER__gen_font_bitmap_Debug /home/<USER>/workspace/projects/local/gen_font_bitmap/src/main.cpp || cmake_object_order_depends_target_gen_font_bitmap
  DEP_FILE = CMakeFiles/gen_font_bitmap.dir/src/main.cpp.o.d
  FLAGS = -g -std=gnu++14
  INCLUDES = -I/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/include/freetype2
  OBJECT_DIR = CMakeFiles/gen_font_bitmap.dir
  OBJECT_FILE_DIR = CMakeFiles/gen_font_bitmap.dir/src

build CMakeFiles/gen_font_bitmap.dir/src/font_renderer.cpp.o: CXX_COMPILER__gen_font_bitmap_Debug /home/<USER>/workspace/projects/local/gen_font_bitmap/src/font_renderer.cpp || cmake_object_order_depends_target_gen_font_bitmap
  DEP_FILE = CMakeFiles/gen_font_bitmap.dir/src/font_renderer.cpp.o.d
  FLAGS = -g -std=gnu++14
  INCLUDES = -I/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/include/freetype2
  OBJECT_DIR = CMakeFiles/gen_font_bitmap.dir
  OBJECT_FILE_DIR = CMakeFiles/gen_font_bitmap.dir/src

build CMakeFiles/gen_font_bitmap.dir/src/bitmap_converter.cpp.o: CXX_COMPILER__gen_font_bitmap_Debug /home/<USER>/workspace/projects/local/gen_font_bitmap/src/bitmap_converter.cpp || cmake_object_order_depends_target_gen_font_bitmap
  DEP_FILE = CMakeFiles/gen_font_bitmap.dir/src/bitmap_converter.cpp.o.d
  FLAGS = -g -std=gnu++14
  INCLUDES = -I/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/include/freetype2
  OBJECT_DIR = CMakeFiles/gen_font_bitmap.dir
  OBJECT_FILE_DIR = CMakeFiles/gen_font_bitmap.dir/src

build CMakeFiles/gen_font_bitmap.dir/src/command_line.cpp.o: CXX_COMPILER__gen_font_bitmap_Debug /home/<USER>/workspace/projects/local/gen_font_bitmap/src/command_line.cpp || cmake_object_order_depends_target_gen_font_bitmap
  DEP_FILE = CMakeFiles/gen_font_bitmap.dir/src/command_line.cpp.o.d
  FLAGS = -g -std=gnu++14
  INCLUDES = -I/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/include/freetype2
  OBJECT_DIR = CMakeFiles/gen_font_bitmap.dir
  OBJECT_FILE_DIR = CMakeFiles/gen_font_bitmap.dir/src

build CMakeFiles/gen_font_bitmap.dir/src/pixel_format.cpp.o: CXX_COMPILER__gen_font_bitmap_Debug /home/<USER>/workspace/projects/local/gen_font_bitmap/src/pixel_format.cpp || cmake_object_order_depends_target_gen_font_bitmap
  DEP_FILE = CMakeFiles/gen_font_bitmap.dir/src/pixel_format.cpp.o.d
  FLAGS = -g -std=gnu++14
  INCLUDES = -I/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/include/freetype2
  OBJECT_DIR = CMakeFiles/gen_font_bitmap.dir
  OBJECT_FILE_DIR = CMakeFiles/gen_font_bitmap.dir/src

build CMakeFiles/gen_font_bitmap.dir/src/bin2bmp.cpp.o: CXX_COMPILER__gen_font_bitmap_Debug /home/<USER>/workspace/projects/local/gen_font_bitmap/src/bin2bmp.cpp || cmake_object_order_depends_target_gen_font_bitmap
  DEP_FILE = CMakeFiles/gen_font_bitmap.dir/src/bin2bmp.cpp.o.d
  FLAGS = -g -std=gnu++14
  INCLUDES = -I/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/include/freetype2
  OBJECT_DIR = CMakeFiles/gen_font_bitmap.dir
  OBJECT_FILE_DIR = CMakeFiles/gen_font_bitmap.dir/src


# =============================================================================
# Link build statements for EXECUTABLE target gen_font_bitmap


#############################################
# Link the executable gen_font_bitmap

build gen_font_bitmap: CXX_EXECUTABLE_LINKER__gen_font_bitmap_Debug CMakeFiles/gen_font_bitmap.dir/src/main.cpp.o CMakeFiles/gen_font_bitmap.dir/src/font_renderer.cpp.o CMakeFiles/gen_font_bitmap.dir/src/bitmap_converter.cpp.o CMakeFiles/gen_font_bitmap.dir/src/command_line.cpp.o CMakeFiles/gen_font_bitmap.dir/src/pixel_format.cpp.o CMakeFiles/gen_font_bitmap.dir/src/bin2bmp.cpp.o
  FLAGS = -g
  LINK_LIBRARIES = -Wl,-rpath,/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/lib:  -lfreetype
  LINK_PATH = -L/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/lib
  OBJECT_DIR = CMakeFiles/gen_font_bitmap.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = gen_font_bitmap
  TARGET_PDB = gen_font_bitmap.dbg

# =============================================================================
# Object build statements for EXECUTABLE target bin2bmp


#############################################
# Order-only phony target for bin2bmp

build cmake_object_order_depends_target_bin2bmp: phony || CMakeFiles/bin2bmp.dir

build CMakeFiles/bin2bmp.dir/src/bin2bmp_main.cpp.o: CXX_COMPILER__bin2bmp_Debug /home/<USER>/workspace/projects/local/gen_font_bitmap/src/bin2bmp_main.cpp || cmake_object_order_depends_target_bin2bmp
  DEP_FILE = CMakeFiles/bin2bmp.dir/src/bin2bmp_main.cpp.o.d
  FLAGS = -g -std=gnu++14
  INCLUDES = -I/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/include/freetype2
  OBJECT_DIR = CMakeFiles/bin2bmp.dir
  OBJECT_FILE_DIR = CMakeFiles/bin2bmp.dir/src

build CMakeFiles/bin2bmp.dir/src/bin2bmp.cpp.o: CXX_COMPILER__bin2bmp_Debug /home/<USER>/workspace/projects/local/gen_font_bitmap/src/bin2bmp.cpp || cmake_object_order_depends_target_bin2bmp
  DEP_FILE = CMakeFiles/bin2bmp.dir/src/bin2bmp.cpp.o.d
  FLAGS = -g -std=gnu++14
  INCLUDES = -I/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/include/freetype2
  OBJECT_DIR = CMakeFiles/bin2bmp.dir
  OBJECT_FILE_DIR = CMakeFiles/bin2bmp.dir/src

build CMakeFiles/bin2bmp.dir/src/bitmap_converter.cpp.o: CXX_COMPILER__bin2bmp_Debug /home/<USER>/workspace/projects/local/gen_font_bitmap/src/bitmap_converter.cpp || cmake_object_order_depends_target_bin2bmp
  DEP_FILE = CMakeFiles/bin2bmp.dir/src/bitmap_converter.cpp.o.d
  FLAGS = -g -std=gnu++14
  INCLUDES = -I/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/include/freetype2
  OBJECT_DIR = CMakeFiles/bin2bmp.dir
  OBJECT_FILE_DIR = CMakeFiles/bin2bmp.dir/src

build CMakeFiles/bin2bmp.dir/src/pixel_format.cpp.o: CXX_COMPILER__bin2bmp_Debug /home/<USER>/workspace/projects/local/gen_font_bitmap/src/pixel_format.cpp || cmake_object_order_depends_target_bin2bmp
  DEP_FILE = CMakeFiles/bin2bmp.dir/src/pixel_format.cpp.o.d
  FLAGS = -g -std=gnu++14
  INCLUDES = -I/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/include/freetype2
  OBJECT_DIR = CMakeFiles/bin2bmp.dir
  OBJECT_FILE_DIR = CMakeFiles/bin2bmp.dir/src


# =============================================================================
# Link build statements for EXECUTABLE target bin2bmp


#############################################
# Link the executable bin2bmp

build bin2bmp: CXX_EXECUTABLE_LINKER__bin2bmp_Debug CMakeFiles/bin2bmp.dir/src/bin2bmp_main.cpp.o CMakeFiles/bin2bmp.dir/src/bin2bmp.cpp.o CMakeFiles/bin2bmp.dir/src/bitmap_converter.cpp.o CMakeFiles/bin2bmp.dir/src/pixel_format.cpp.o
  FLAGS = -g
  LINK_PATH = -L/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/lib
  OBJECT_DIR = CMakeFiles/bin2bmp.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin2bmp
  TARGET_PDB = bin2bmp.dbg

# =============================================================================
# Object build statements for EXECUTABLE target vz_font


#############################################
# Order-only phony target for vz_font

build cmake_object_order_depends_target_vz_font: phony || CMakeFiles/vz_font.dir

build CMakeFiles/vz_font.dir/src/bin2bmp.cpp.o: CXX_COMPILER__vz_font_Debug /home/<USER>/workspace/projects/local/gen_font_bitmap/src/bin2bmp.cpp || cmake_object_order_depends_target_vz_font
  DEP_FILE = CMakeFiles/vz_font.dir/src/bin2bmp.cpp.o.d
  FLAGS = -g -std=gnu++14
  INCLUDES = -I/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/include/freetype2
  OBJECT_DIR = CMakeFiles/vz_font.dir
  OBJECT_FILE_DIR = CMakeFiles/vz_font.dir/src

build CMakeFiles/vz_font.dir/src/pixel_format.cpp.o: CXX_COMPILER__vz_font_Debug /home/<USER>/workspace/projects/local/gen_font_bitmap/src/pixel_format.cpp || cmake_object_order_depends_target_vz_font
  DEP_FILE = CMakeFiles/vz_font.dir/src/pixel_format.cpp.o.d
  FLAGS = -g -std=gnu++14
  INCLUDES = -I/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/include/freetype2
  OBJECT_DIR = CMakeFiles/vz_font.dir
  OBJECT_FILE_DIR = CMakeFiles/vz_font.dir/src

build CMakeFiles/vz_font.dir/vz/chartab.cpp.o: CXX_COMPILER__vz_font_Debug /home/<USER>/workspace/projects/local/gen_font_bitmap/vz/chartab.cpp || cmake_object_order_depends_target_vz_font
  DEP_FILE = CMakeFiles/vz_font.dir/vz/chartab.cpp.o.d
  FLAGS = -g -std=gnu++14
  INCLUDES = -I/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/include/freetype2
  OBJECT_DIR = CMakeFiles/vz_font.dir
  OBJECT_FILE_DIR = CMakeFiles/vz_font.dir/vz

build CMakeFiles/vz_font.dir/vz/vzft.cpp.o: CXX_COMPILER__vz_font_Debug /home/<USER>/workspace/projects/local/gen_font_bitmap/vz/vzft.cpp || cmake_object_order_depends_target_vz_font
  DEP_FILE = CMakeFiles/vz_font.dir/vz/vzft.cpp.o.d
  FLAGS = -g -std=gnu++14
  INCLUDES = -I/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/include/freetype2
  OBJECT_DIR = CMakeFiles/vz_font.dir
  OBJECT_FILE_DIR = CMakeFiles/vz_font.dir/vz

build CMakeFiles/vz_font.dir/vz/char_code.cpp.o: CXX_COMPILER__vz_font_Debug /home/<USER>/workspace/projects/local/gen_font_bitmap/vz/char_code.cpp || cmake_object_order_depends_target_vz_font
  DEP_FILE = CMakeFiles/vz_font.dir/vz/char_code.cpp.o.d
  FLAGS = -g -std=gnu++14
  INCLUDES = -I/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/include/freetype2
  OBJECT_DIR = CMakeFiles/vz_font.dir
  OBJECT_FILE_DIR = CMakeFiles/vz_font.dir/vz

build CMakeFiles/vz_font.dir/vz/char_code_c.c.o: C_COMPILER__vz_font_Debug /home/<USER>/workspace/projects/local/gen_font_bitmap/vz/char_code_c.c || cmake_object_order_depends_target_vz_font
  DEP_FILE = CMakeFiles/vz_font.dir/vz/char_code_c.c.o.d
  FLAGS = -g
  INCLUDES = -I/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/include/freetype2
  OBJECT_DIR = CMakeFiles/vz_font.dir
  OBJECT_FILE_DIR = CMakeFiles/vz_font.dir/vz

build CMakeFiles/vz_font.dir/vz/vz_font_test.cpp.o: CXX_COMPILER__vz_font_Debug /home/<USER>/workspace/projects/local/gen_font_bitmap/vz/vz_font_test.cpp || cmake_object_order_depends_target_vz_font
  DEP_FILE = CMakeFiles/vz_font.dir/vz/vz_font_test.cpp.o.d
  FLAGS = -g -std=gnu++14
  INCLUDES = -I/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/include/freetype2
  OBJECT_DIR = CMakeFiles/vz_font.dir
  OBJECT_FILE_DIR = CMakeFiles/vz_font.dir/vz


# =============================================================================
# Link build statements for EXECUTABLE target vz_font


#############################################
# Link the executable vz_font

build vz_font: CXX_EXECUTABLE_LINKER__vz_font_Debug CMakeFiles/vz_font.dir/src/bin2bmp.cpp.o CMakeFiles/vz_font.dir/src/pixel_format.cpp.o CMakeFiles/vz_font.dir/vz/chartab.cpp.o CMakeFiles/vz_font.dir/vz/vzft.cpp.o CMakeFiles/vz_font.dir/vz/char_code.cpp.o CMakeFiles/vz_font.dir/vz/char_code_c.c.o CMakeFiles/vz_font.dir/vz/vz_font_test.cpp.o
  FLAGS = -g
  LINK_LIBRARIES = -Wl,-rpath,/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/lib:  -lfreetype
  LINK_PATH = -L/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/lib
  OBJECT_DIR = CMakeFiles/vz_font.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = vz_font
  TARGET_PDB = vz_font.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/workspace/projects/local/gen_font_bitmap/build && /usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/workspace/projects/local/gen_font_bitmap/build && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/workspace/projects/local/gen_font_bitmap -B/home/<USER>/workspace/projects/local/gen_font_bitmap/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for install

build CMakeFiles/install.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/workspace/projects/local/gen_font_bitmap/build && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles/install.util


#############################################
# Utility command for install/local

build CMakeFiles/install/local.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/workspace/projects/local/gen_font_bitmap/build && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install/local: phony CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build CMakeFiles/install/strip.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/workspace/projects/local/gen_font_bitmap/build && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build install/strip: phony CMakeFiles/install/strip.util

# =============================================================================
# Target aliases.

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /home/<USER>/workspace/projects/local/gen_font_bitmap/build

build all: phony gen_font_bitmap bin2bmp vz_font

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | ../CMakeLists.txt /usr/share/cmake-3.22/Modules/CMakeCInformation.cmake /usr/share/cmake-3.22/Modules/CMakeCXXInformation.cmake /usr/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake-3.22/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake /usr/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake-3.22/Modules/Compiler/GNU-C.cmake /usr/share/cmake-3.22/Modules/Compiler/GNU-CXX.cmake /usr/share/cmake-3.22/Modules/Compiler/GNU.cmake /usr/share/cmake-3.22/Modules/Platform/Linux-GNU-C.cmake /usr/share/cmake-3.22/Modules/Platform/Linux-GNU-CXX.cmake /usr/share/cmake-3.22/Modules/Platform/Linux-GNU.cmake /usr/share/cmake-3.22/Modules/Platform/Linux.cmake /usr/share/cmake-3.22/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.22.1/CMakeCCompiler.cmake CMakeFiles/3.22.1/CMakeCXXCompiler.cmake CMakeFiles/3.22.1/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build ../CMakeLists.txt /usr/share/cmake-3.22/Modules/CMakeCInformation.cmake /usr/share/cmake-3.22/Modules/CMakeCXXInformation.cmake /usr/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake-3.22/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake /usr/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake-3.22/Modules/Compiler/GNU-C.cmake /usr/share/cmake-3.22/Modules/Compiler/GNU-CXX.cmake /usr/share/cmake-3.22/Modules/Compiler/GNU.cmake /usr/share/cmake-3.22/Modules/Platform/Linux-GNU-C.cmake /usr/share/cmake-3.22/Modules/Platform/Linux-GNU-CXX.cmake /usr/share/cmake-3.22/Modules/Platform/Linux-GNU.cmake /usr/share/cmake-3.22/Modules/Platform/Linux.cmake /usr/share/cmake-3.22/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.22.1/CMakeCCompiler.cmake CMakeFiles/3.22.1/CMakeCXXCompiler.cmake CMakeFiles/3.22.1/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
