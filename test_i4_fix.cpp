#include "font_renderer.h"
#include "bin2bmp.h"
#include <iostream>
#include <fstream>

int main() {
    FontRenderer fontRenderer;
    if (!fontRenderer.initialize()) {
        std::cerr << "Failed to initialize font renderer" << std::endl;
        return 1;
    }

    if (!fontRenderer.loadFont("fonts/DroidSans.ttf", 24)) {
        std::cerr << "Failed to load font" << std::endl;
        return 1;
    }

    initGlobalPalette();

    std::cout << "Testing I4 format fix:" << std::endl;

    // 测试简单的单行文本
    std::string testText = "Hello World";
    Bitmap bitmap1 = fontRenderer.renderText(testText, Color::fromIndex(1),
                                            PixelFormat::I4, 1, Color::fromIndex(3), 0);

    std::cout << "Single line I4 bitmap: " << bitmap1.width << "x" << bitmap1.height << std::endl;

    // 测试自动换行文本
    std::string wrapText = "This is a longer text that should wrap to multiple lines";
    Bitmap bitmap2 = fontRenderer.renderText(wrapText, AutoWrapAlignment::LEFT, 150, -1,
                                            Color::fromIndex(1), PixelFormat::I4, 1, Color::fromIndex(3), 0);

    std::cout << "Auto-wrap I4 bitmap: " << bitmap2.width << "x" << bitmap2.height << std::endl;

    // 保存测试文件
    std::ofstream file1("test_i4_single.bin", std::ios::binary);
    if (file1.is_open()) {
        file1.write(reinterpret_cast<const char*>(bitmap1.buffer.data()), bitmap1.buffer.size());
        file1.close();
        std::cout << "✓ Saved test_i4_single.bin" << std::endl;
    }

    std::ofstream file2("test_i4_wrap.bin", std::ios::binary);
    if (file2.is_open()) {
        file2.write(reinterpret_cast<const char*>(bitmap2.buffer.data()), bitmap2.buffer.size());
        file2.close();
        std::cout << "✓ Saved test_i4_wrap.bin" << std::endl;
    }

    // 转换为 BMP 以便查看
    BinToBMP converter;
    converter.convert("test_i4_single.bin", "test_i4_single.bmp", bitmap1.width, bitmap1.height, PixelFormat::I4, "");
    converter.convert("test_i4_wrap.bin", "test_i4_wrap.bmp", bitmap2.width, bitmap2.height, PixelFormat::I4, "");

    std::cout << "✓ I4 format test completed!" << std::endl;

    return 0;
}
