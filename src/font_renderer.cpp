#include "font_renderer.h"
#include <iostream>
#include <cstring>
#include <algorithm>
#include <cmath>
#include <limits>

FontRenderer::FontRenderer() : m_library(nullptr), m_face(nullptr), m_initialized(false) {
    // 初始化默认调色板（灰度）
    for (int i = 0; i < PALETTE_SIZE; i++) {
        unsigned char value = static_cast<unsigned char>((i * 255) / (PALETTE_SIZE - 1));
        m_palette[i] = Color(255, value, value, value);
    }
}

FontRenderer::~FontRenderer() {
    clearCache();
    if (m_face) {
        FT_Done_Face(m_face);
    }

    if (m_initialized) {
        FT_Done_FreeType(m_library);
    }
}

bool FontRenderer::initialize() {
    FT_Error error = FT_Init_FreeType(&m_library);
    if (error) {
        std::cerr << "Error initializing FreeType library" << std::endl;
        return false;
    }

    m_initialized = true;
    return true;
}

bool FontRenderer::loadFont(const std::string& fontPath, int fontSize) {
    if (!m_initialized) {
        std::cerr << "FreeType library not initialized" << std::endl;
        return false;
    }

    // If a face was previously loaded, clean it up
    if (m_face) {
        FT_Done_Face(m_face);
        m_face = nullptr;
    }

    FT_Error error = FT_New_Face(m_library, fontPath.c_str(), 0, &m_face);
    if (error) {
        std::cerr << "Error loading font: " << fontPath << std::endl;
        return false;
    }

    // Set font size
    error = FT_Set_Pixel_Sizes(m_face, 0, fontSize);
    if (error) {
        std::cerr << "Error setting font size" << std::endl;
        FT_Done_Face(m_face);
        m_face = nullptr;
        return false;
    }

    return true;
}

int FontRenderer::decodeUTF8Char(const std::string& text, size_t index, FT_UInt& charCode) {
    // 初始化字符码和字符长度
    charCode = 0;
    int charLen = 0;

    // 处理UTF-8编码
    if ((text[index] & 0x80) == 0) {
        // 1字节字符
        charCode = text[index];
        charLen = 1;
    } else if ((text[index] & 0xE0) == 0xC0) {
        // 2字节字符
        if (index + 1 < text.length()) {
            charCode = ((text[index] & 0x1F) << 6) | (text[index+1] & 0x3F);
            charLen = 2;
        }
    } else if ((text[index] & 0xF0) == 0xE0) {
        // 3字节字符
        if (index + 2 < text.length()) {
            charCode = ((text[index] & 0x0F) << 12) | ((text[index+1] & 0x3F) << 6) | (text[index+2] & 0x3F);
            charLen = 3;
        }
    } else if ((text[index] & 0xF8) == 0xF0) {
        // 4字节字符
        if (index + 3 < text.length()) {
            charCode = ((text[index] & 0x07) << 18) | ((text[index+1] & 0x3F) << 12) |
                       ((text[index+2] & 0x3F) << 6) | (text[index+3] & 0x3F);
            charLen = 4;
        }
    }

    return charLen;
}

bool FontRenderer::isCombiningCharacter(FT_UInt charCode) {
    // 泰文组合字符范围
    // 上标符号（声调符号）: U+0E31, U+0E34-U+0E3A
    // 下标符号: U+0E47-U+0E4E
    // 特殊符号: U+0E33 (SARA AM)
    if (charCode == 0x0E31 ||  // SARA AM
        (charCode >= 0x0E34 && charCode <= 0x0E3A) ||  // 上标元音
        (charCode >= 0x0E47 && charCode <= 0x0E4E) ||  // 下标符号和声调符号
        charCode == 0x0E33) {  // SARA AM (特殊处理)
        return true;
    }

    // 其他语言的组合字符范围
    // 阿拉伯文组合字符: U+064B-U+065F, U+0670, U+06D6-U+06ED
    if ((charCode >= 0x064B && charCode <= 0x065F) ||
        charCode == 0x0670 ||
        (charCode >= 0x06D6 && charCode <= 0x06ED)) {
        return true;
    }

    // 印度文组合字符: U+0900-U+097F范围内的一些字符
    if (charCode >= 0x0900 && charCode <= 0x097F) {
        // 简化处理，将大部分印度文组合字符视为组合字符
        if ((charCode >= 0x0901 && charCode <= 0x0903) ||
            (charCode >= 0x093C && charCode <= 0x094F) ||
            (charCode >= 0x0951 && charCode <= 0x0957) ||
            (charCode >= 0x0962 && charCode <= 0x0963)) {
            return true;
        }
    }

    // Unicode组合字符类别（简化处理）
    // 组合附加符号: U+0300-U+036F
    if (charCode >= 0x0300 && charCode <= 0x036F) {
        return true;
    }

    return false;
}

bool FontRenderer::isFullWidthCharacter(FT_UInt charCode) {
    // 中文汉字范围
    if ((charCode >= 0x4E00 && charCode <= 0x9FFF) ||     // CJK统一表意文字
        (charCode >= 0x3400 && charCode <= 0x4DBF) ||     // CJK扩展A
        (charCode >= 0x20000 && charCode <= 0x2A6DF) ||   // CJK扩展B
        (charCode >= 0x2A700 && charCode <= 0x2B73F) ||   // CJK扩展C
        (charCode >= 0x2B740 && charCode <= 0x2B81F) ||   // CJK扩展D
        (charCode >= 0x2B820 && charCode <= 0x2CEAF)) {   // CJK扩展E
        return true;
    }

    // 日文平假名和片假名
    if ((charCode >= 0x3040 && charCode <= 0x309F) ||     // 平假名
        (charCode >= 0x30A0 && charCode <= 0x30FF)) {     // 片假名
        return true;
    }

    // 韩文音节
    if (charCode >= 0xAC00 && charCode <= 0xD7AF) {       // 韩文音节
        return true;
    }

    // 全角符号和数字
    if (charCode >= 0xFF00 && charCode <= 0xFFEF) {       // 全角字符
        return true;
    }

    // 中文标点符号
    if ((charCode >= 0x3000 && charCode <= 0x303F) ||     // CJK符号和标点
        (charCode >= 0x2E80 && charCode <= 0x2EFF) ||     // CJK部首补充
        (charCode >= 0x2F00 && charCode <= 0x2FDF) ||     // 康熙部首
        (charCode >= 0x31C0 && charCode <= 0x31EF)) {     // CJK笔划
        return true;
    }

    return false;
}

bool FontRenderer::calculateTextDimensions(const std::string& text, int outlineWidth, int& totalWidth, int& totalHeight, int& maxAscender, int& maxDescender, int charSpacing) {
    if (!m_face) {
        std::cerr << "No font loaded" << std::endl;
        return false;
    }

    // 检查缓存
    std::string cacheKey = generateCacheKey(text, outlineWidth, charSpacing);
    auto it = m_dimensionCache.find(cacheKey);
    if (it != m_dimensionCache.end()) {
        totalWidth = it->second.first;
        totalHeight = it->second.second;
        // 从缓存中恢复 ascender 和 descender（简化估算）
        int fontAscender = m_face->size->metrics.ascender >> 6;
        int fontDescender = -(m_face->size->metrics.descender >> 6);
        int totalHeightWithoutOutline = totalHeight - outlineWidth * 2;

        // 按比例分配 ascender 和 descender
        maxAscender = totalHeightWithoutOutline * fontAscender / (fontAscender + fontDescender);
        maxDescender = totalHeightWithoutOutline - maxAscender;

        return true;
    }

    // 初始化参数
    totalWidth = 0;
    int maxHeight = 0;
    maxAscender = 0;
    maxDescender = 0;

    for (size_t i = 0; i < text.length(); /* increment in loop */) {
        FT_UInt charCode = 0;
        int charLen = decodeUTF8Char(text, i, charCode);

        if (charLen == 0) {
            // 无效的UTF-8序列
            i++;
            continue;
        }

        // 使用缓存获取字形信息
        const GlyphInfo& glyphInfo = getGlyphInfo(charCode);

        if (!glyphInfo.isCombining) {
            // 非组合字符：正常添加advance和字符间距
            totalWidth += glyphInfo.advance + charSpacing;
        }
        // 组合字符不增加总宽度，因为它们与基础字符重叠

        maxHeight = std::max(maxHeight, glyphInfo.height);
        maxAscender = std::max(maxAscender, glyphInfo.bearingY);
        maxDescender = std::max(maxDescender, glyphInfo.height - glyphInfo.bearingY);

        i += charLen;
    }

    // 为描边添加空间
    totalWidth += outlineWidth * 2;

    // 优化的 ascender 和 descender 计算
    // 目标：减少不必要的顶部空白，同时避免削顶

    // 获取字体的全局度量信息
    int fontAscender = m_face->size->metrics.ascender >> 6;
    int fontDescender = -(m_face->size->metrics.descender >> 6);

    // 使用实际计算的值，但要考虑描边的影响
    // 描边会扩展字符的边界，特别是在顶部和底部

    int fontHeight = m_face->size->metrics.height >> 6;

    // 计算描边所需的额外空间
    // 描边在各个方向都会扩展 outlineWidth 像素
    int outlineExpansion = outlineWidth;

    // 安全边距：为了彻底避免削顶，特别是高上升字符和描边效果
    // 对于有描边的情况，增加额外的安全边距
    int safetyMargin = (outlineWidth > 0) ? 2 : std::max(2, static_cast<int>(fontHeight * 0.05));

    // 对于 ascender，使用计算值 + 描边扩展 + 安全边距
    // 但不超过字体全局值 + 描边扩展
    maxAscender = std::min(maxAscender + outlineExpansion + safetyMargin,
                          fontAscender + outlineExpansion);

    // 对于 descender，也要考虑描边扩展
    int minDescender = static_cast<int>(fontDescender * 0.3);
    maxDescender = std::max(maxDescender, minDescender) + outlineExpansion + safetyMargin / 2;

    totalHeight = maxAscender + maxDescender + outlineWidth * 2;

    // 将结果放入缓存
    m_dimensionCache[cacheKey] = std::make_pair(totalWidth, totalHeight);

    return true;
}

bool FontRenderer::getTextDimensions(const std::string& text, int outlineWidth, int& width, int& height, int charSpacing) {
    int totalWidth, totalHeight, maxAscender, maxDescender;

    // 使用辅助方法计算文本尺寸
    if (!calculateTextDimensions(text, outlineWidth, totalWidth, totalHeight, maxAscender, maxDescender, charSpacing)) {
        return false;
    }

    // 设置输出参数
    width = totalWidth;
    height = totalHeight;

    return true;
}

Bitmap FontRenderer::renderText(const std::string& text, const Color& color,
                               PixelFormat format, int outlineWidth,
                               const Color& outlineColor, int charSpacing) {
    if (!m_face) {
        std::cerr << "No font loaded" << std::endl;
        return Bitmap();
    }

    // 使用全局调色板
    m_palette = g_globalPalette;

    // 使用辅助方法计算文本尺寸
    int totalWidth, totalHeight, maxAscender, maxDescender;
    if (!calculateTextDimensions(text, outlineWidth, totalWidth, totalHeight, maxAscender, maxDescender, charSpacing)) {
        return Bitmap();
    }

    // 创建指定格式的位图
    Bitmap bitmap(totalWidth, totalHeight, format);

    // 第二遍：渲染文本到位图
    int penX = outlineWidth;
    int penY = maxAscender + outlineWidth;
    int lastBasePenX = penX;  // 记录上一个基础字符的位置

    for (size_t i = 0; i < text.length(); /* increment in loop */) {
        FT_UInt charCode = 0;
        int charLen = decodeUTF8Char(text, i, charCode);

        if (charLen == 0) {
            i++;
            continue;
        }

        FT_UInt glyphIndex = FT_Get_Char_Index(m_face, charCode);
        FT_Error error = FT_Load_Glyph(m_face, glyphIndex, FT_LOAD_DEFAULT);
        if (error) {
            i += charLen;
            continue;
        }

        error = FT_Render_Glyph(m_face->glyph, FT_RENDER_MODE_NORMAL);
        if (error) {
            i += charLen;
            continue;
        }

        bool isCombining = isCombiningCharacter(charCode);
        int renderPenX = isCombining ? lastBasePenX : penX;

        // 根据位图格式选择不同的渲染方法
        switch (format) {
            case PixelFormat::ARGB8888:
                renderGlyphARGB8888(m_face->glyph, bitmap, renderPenX, penY, color);
                break;
            case PixelFormat::ARGB1555:
                renderGlyphARGB1555(m_face->glyph, bitmap, renderPenX, penY, color);
                break;
            case PixelFormat::I4:
                renderGlyphI4(m_face->glyph, bitmap, renderPenX, penY, color);
                break;
        }

        // 处理笔位置移动
        int advance = m_face->glyph->advance.x >> 6;

        if (!isCombining) {
            // 非组合字符：正常移动笔位置并添加字符间距
            penX += advance + charSpacing;
            lastBasePenX = penX;  // 更新基础字符位置
        } else {
            // 组合字符：仍然要考虑advance，但不添加字符间距
            // 这样可以正确处理有advance的组合字符
            penX += advance;
        }

        i += charLen;
    }

    // 添加描边
    if (outlineWidth > 0) {
        addOutline(bitmap, outlineWidth, outlineColor);
    }

    return bitmap;
}

// 自动换行的renderText方法
Bitmap FontRenderer::renderText(const std::string& text, AutoWrapAlignment align, int max_width, int max_height, const Color& color,
                               PixelFormat format, int outlineWidth, const Color& outlineColor, int charSpacing, WrapMode wrapMode) {
    if (!m_face) {
        std::cerr << "No font loaded" << std::endl;
        return Bitmap();
    }

    // 使用全局调色板
    m_palette = g_globalPalette;

    // 根据换行模式选择不同的处理方式
    std::vector<LineInfo> lineInfos;
    if (wrapMode == SIMPLE_WRAP) {
        lineInfos = breakTextIntoLinesSimple(text, max_width, max_height, outlineWidth, charSpacing);
    } else {
        lineInfos = breakTextIntoLines(text, max_width, max_height, outlineWidth, charSpacing);
    }


    // 渲染行信息列表，传递最大宽度限制
    return renderLineInfos(lineInfos, align, color, format, outlineWidth, outlineColor, charSpacing, max_width);
}

// 字符串数组的renderText方法
Bitmap FontRenderer::renderText(const std::vector<std::string>& textLines, AutoWrapAlignment align, const Color& color,
                               PixelFormat format, int outlineWidth, const Color& outlineColor, int charSpacing) {
    if (!m_face || textLines.empty()) {
        std::cerr << "No font loaded or empty text" << std::endl;
        return Bitmap();
    }

    // 使用全局调色板
    m_palette = g_globalPalette;

    // 使用新的底层接口转换为行信息
    std::vector<LineInfo> lineInfos = convertToLineInfo(textLines, outlineWidth, charSpacing);

    // 渲染行信息列表（不限制最大宽度）
    return renderLineInfos(lineInfos, align, color, format, outlineWidth, outlineColor, charSpacing, -1);
}

// 将源位图复制到目标位图的指定位置
void FontRenderer::copyBitmapToTarget(const Bitmap& source, Bitmap& target, int offsetX, int offsetY) {
    if (source.format != target.format) {
        std::cerr << "Source and target bitmaps must have the same format" << std::endl;
        return;
    }

    switch (source.format) {
        case PixelFormat::ARGB8888:
        case PixelFormat::ARGB1555: {
            int bytesPerPixel = (source.format == PixelFormat::ARGB8888) ? 4 : 2;

            for (int y = 0; y < source.height; y++) {
                int targetY = offsetY + y;
                if (targetY < 0 || targetY >= target.height) continue;

                for (int x = 0; x < source.width; x++) {
                    int targetX = offsetX + x;
                    if (targetX < 0 || targetX >= target.width) continue;

                    int sourceIdx = (y * source.width + x) * bytesPerPixel;
                    int targetIdx = (targetY * target.width + targetX) * bytesPerPixel;

                    // 复制像素数据
                    for (int b = 0; b < bytesPerPixel; b++) {
                        target.buffer[targetIdx + b] = source.buffer[sourceIdx + b];
                    }
                }
            }
            break;
        }

        case PixelFormat::I4: {
            for (int y = 0; y < source.height; y++) {
                int targetY = offsetY + y;
                if (targetY < 0 || targetY >= target.height) continue;

                for (int x = 0; x < source.width; x++) {
                    int targetX = offsetX + x;
                    if (targetX < 0 || targetX >= target.width) continue;

                    // I4格式的像素复制
                    int sourceByteIdx = y * source.pitch + x / 2;
                    int targetByteIdx = targetY * target.pitch + targetX / 2;

                    unsigned char sourcePixel;
                    if (x % 2 == 0) {
                        // 高半字节
                        sourcePixel = (source.buffer[sourceByteIdx] >> 4) & 0x0F;
                    } else {
                        // 低半字节
                        sourcePixel = source.buffer[sourceByteIdx] & 0x0F;
                    }

                    // 只复制非透明像素
                    if (sourcePixel > 0) {
                        if (targetX % 2 == 0) {
                            // 高半字节
                            target.buffer[targetByteIdx] = (sourcePixel << 4) | (target.buffer[targetByteIdx] & 0x0F);
                        } else {
                            // 低半字节
                            target.buffer[targetByteIdx] = (target.buffer[targetByteIdx] & 0xF0) | sourcePixel;
                        }
                    }
                }
            }
            break;
        }
    }
}

// 将文本分解为行信息（支持自动换行）
std::vector<LineInfo> FontRenderer::breakTextIntoLines(const std::string& text, int maxWidth, int maxHeight,
                                                       int outlineWidth, int charSpacing) {
    std::vector<LineInfo> lines;
    if (!m_face || text.empty()) {
        return lines;
    }

    std::string currentLine;
    std::string currentWord;

    for (size_t i = 0; i < text.length(); /* increment in loop */) {
        FT_UInt charCode = 0;
        int charLen = decodeUTF8Char(text, i, charCode);

        if (charLen == 0) {
            i++;
            continue;
        }

        // 检查是否是空格、换行符或中文字符
        if (charCode == ' ' || charCode == '\t') {
            // 空格处理：先将当前单词添加到行中
            if (!currentWord.empty()) {
                std::string testLine = currentLine.empty() ? currentWord : currentLine + " " + currentWord;
                int testWidth, testHeight, testAscender, testDescender;
                if (calculateTextDimensions(testLine, outlineWidth, testWidth, testHeight, testAscender, testDescender, charSpacing)) {
                    if (testWidth > maxWidth && !currentLine.empty()) {
                        // 超过宽度，换行
                        int lineWidth, lineHeight, lineAscender, lineDescender;
                        calculateTextDimensions(currentLine, outlineWidth, lineWidth, lineHeight, lineAscender, lineDescender, charSpacing);
                        lines.emplace_back(currentLine, lineWidth, lineHeight, lineAscender, lineDescender);

                        currentLine = currentWord;
                    } else {
                        // 不超过宽度，继续添加
                        currentLine = testLine;
                    }
                } else {
                    // 计算失败，直接添加
                    currentLine = currentLine.empty() ? currentWord : currentLine + " " + currentWord;
                }
                currentWord.clear();
            }
            // 空格本身不添加到文本中，只作为分隔符
        } else if (charCode == '\n') {
            // 强制换行
            currentLine += currentWord;
            int lineWidth, lineHeight, lineAscender, lineDescender;
            calculateTextDimensions(currentLine, outlineWidth, lineWidth, lineHeight, lineAscender, lineDescender, charSpacing);
            lines.emplace_back(currentLine, lineWidth, lineHeight, lineAscender, lineDescender);
            currentLine.clear();
            currentWord.clear();
        } else if (isFullWidthCharacter(charCode)) {
            // 中文等宽字符，可以在任意位置换行
            std::string charStr;
            for (int j = 0; j < charLen; j++) {
                charStr += text[i + j];
            }

            // 先将当前单词添加到当前行（如果有的话）
            if (!currentWord.empty()) {
                if (currentLine.empty()) {
                    currentLine = currentWord;
                } else {
                    currentLine += " " + currentWord;
                }
                currentWord.clear();
            }

            // 现在检查添加中文字符后是否超过宽度
            std::string testLine = currentLine + charStr;
            int testWidth, testHeight, testAscender, testDescender;
            if (calculateTextDimensions(testLine, outlineWidth, testWidth, testHeight, testAscender, testDescender, charSpacing)) {
                if (testWidth <= maxWidth) {
                    // 不超过宽度，添加到当前行
                    currentLine = testLine;
                } else {
                    // 超过宽度，需要换行
                    if (!currentLine.empty()) {
                        // 输出当前行
                        int lineWidth, lineHeight, lineAscender, lineDescender;
                        calculateTextDimensions(currentLine, outlineWidth, lineWidth, lineHeight, lineAscender, lineDescender, charSpacing);
                        lines.emplace_back(currentLine, lineWidth, lineHeight, lineAscender, lineDescender);
                        currentLine = charStr;  // 新行从中文字符开始
                    } else {
                        // 当前行为空，直接添加
                        currentLine = charStr;
                    }
                }
            } else {
                // 计算失败，直接添加
                currentLine += charStr;
            }
        } else {
            // 英文等字符，直接添加到当前单词，不在字符级别检查宽度
            for (int j = 0; j < charLen; j++) {
                currentWord += text[i + j];
            }
        }

        i += charLen;
    }

    // 处理最后的单词和行
    if (!currentWord.empty()) {
        if (!currentLine.empty()) {
            // 当前行和当前单词都不为空
            std::string testLine = currentLine + " " + currentWord;
            int testWidth, testHeight, testAscender, testDescender;
            if (calculateTextDimensions(testLine, outlineWidth, testWidth, testHeight, testAscender, testDescender, charSpacing)) {
                if (testWidth > maxWidth) {
                    // 超过宽度，先添加当前行
                    int lineWidth, lineHeight, lineAscender, lineDescender;
                    calculateTextDimensions(currentLine, outlineWidth, lineWidth, lineHeight, lineAscender, lineDescender, charSpacing);
                    lines.emplace_back(currentLine, lineWidth, lineHeight, lineAscender, lineDescender);

                    // 检查单词是否需要进一步分割（对于中文字符）
                    if (needsWordBreaking(currentWord, maxWidth, outlineWidth, charSpacing)) {
                        breakLongWord(currentWord, maxWidth, outlineWidth, charSpacing, lines);
                    } else {
                        calculateTextDimensions(currentWord, outlineWidth, lineWidth, lineHeight, lineAscender, lineDescender, charSpacing);
                        lines.emplace_back(currentWord, lineWidth, lineHeight, lineAscender, lineDescender);
                    }
                } else {
                    // 不超过宽度，直接添加
                    lines.emplace_back(testLine, testWidth, testHeight, testAscender, testDescender);
                }
            } else {
                // 计算失败，分别添加
                int lineWidth, lineHeight, lineAscender, lineDescender;
                calculateTextDimensions(currentLine, outlineWidth, lineWidth, lineHeight, lineAscender, lineDescender, charSpacing);
                lines.emplace_back(currentLine, lineWidth, lineHeight, lineAscender, lineDescender);
                calculateTextDimensions(currentWord, outlineWidth, lineWidth, lineHeight, lineAscender, lineDescender, charSpacing);
                lines.emplace_back(currentWord, lineWidth, lineHeight, lineAscender, lineDescender);
            }
        } else {
            // 只有当前单词
            if (needsWordBreaking(currentWord, maxWidth, outlineWidth, charSpacing)) {
                breakLongWord(currentWord, maxWidth, outlineWidth, charSpacing, lines);
            } else {
                int lineWidth, lineHeight, lineAscender, lineDescender;
                calculateTextDimensions(currentWord, outlineWidth, lineWidth, lineHeight, lineAscender, lineDescender, charSpacing);
                lines.emplace_back(currentWord, lineWidth, lineHeight, lineAscender, lineDescender);
            }
        }
    } else if (!currentLine.empty()) {
        // 只有当前行
        int lineWidth, lineHeight, lineAscender, lineDescender;
        calculateTextDimensions(currentLine, outlineWidth, lineWidth, lineHeight, lineAscender, lineDescender, charSpacing);
        lines.emplace_back(currentLine, lineWidth, lineHeight, lineAscender, lineDescender);
    }

    // 如果指定了最大高度，限制行数
    if (maxHeight > 0) {
        int currentHeight = 0;
        size_t maxLines = 0;
        for (size_t i = 0; i < lines.size(); i++) {
            currentHeight += lines[i].height;
            if (currentHeight > maxHeight) {
                break;
            }
            maxLines = i + 1;
        }
        if (maxLines < lines.size()) {
            lines.resize(maxLines);
        }
    }

    return lines;
}

// 简化版换行：中英文都可以在任意位置换行（优化版）
std::vector<LineInfo> FontRenderer::breakTextIntoLinesSimple(const std::string& text, int maxWidth, int maxHeight,
                                                            int outlineWidth, int charSpacing) {
    std::vector<LineInfo> lines;
    if (!m_face || text.empty()) {
        return lines;
    }

    // 优化：预分配字符串空间
    std::string currentLine;
    currentLine.reserve(text.length() / 4);  // 估算平均行长度

    // 优化：预计算字符信息
    std::vector<std::pair<size_t, size_t>> charPositions;  // (start, length)
    std::vector<int> charWidths;

    // 第一遍：预处理字符信息
    int totalWidth = 0;
    for (size_t i = 0; i < text.length(); /* increment in loop */) {
        FT_UInt charCode = 0;
        int charLen = decodeUTF8Char(text, i, charCode);

        if (charLen == 0) {
            i++;
            continue;
        }

        charPositions.emplace_back(i, charLen);

        if (charCode == '\n') {
            charWidths.push_back(0);  // 换行符宽度为0
        } else {
            const GlyphInfo& glyphInfo = getGlyphInfo(charCode);
            int charWidth = glyphInfo.isCombining ? 0 : (glyphInfo.advance + charSpacing);
            charWidths.push_back(charWidth);
        }

        i += charLen;
    }

    // 第二遍：按宽度限制分行
    int currentWidth = 0;
    size_t lineStart = 0;

    for (size_t i = 0; i < charPositions.size(); i++) {
        size_t charStart = charPositions[i].first;
        size_t charLen = charPositions[i].second;
        int charWidth = charWidths[i];

        // 检查是否是换行符
        if (text[charStart] == '\n') {
            // 强制换行
            if (lineStart < charPositions.size()) {
                std::string lineText = text.substr(charPositions[lineStart].first,
                                                  charStart - charPositions[lineStart].first);
                int lineWidth, lineHeight, lineAscender, lineDescender;
                calculateTextDimensions(lineText, outlineWidth, lineWidth, lineHeight, lineAscender, lineDescender, charSpacing);
                lines.emplace_back(lineText, lineWidth, lineHeight, lineAscender, lineDescender);
            }
            lineStart = i + 1;
            currentWidth = 0;
        } else if (currentWidth + charWidth > maxWidth && currentWidth > 0) {
            // 超过宽度，换行
            std::string lineText = text.substr(charPositions[lineStart].first,
                                              charStart - charPositions[lineStart].first);
            int lineWidth, lineHeight, lineAscender, lineDescender;
            calculateTextDimensions(lineText, outlineWidth, lineWidth, lineHeight, lineAscender, lineDescender, charSpacing);
            lines.emplace_back(lineText, lineWidth, lineHeight, lineAscender, lineDescender);

            lineStart = i;
            currentWidth = charWidth;
        } else {
            currentWidth += charWidth;
        }
    }

    // 处理最后一行
    if (lineStart < charPositions.size()) {
        std::string lineText = text.substr(charPositions[lineStart].first);
        int lineWidth, lineHeight, lineAscender, lineDescender;
        calculateTextDimensions(lineText, outlineWidth, lineWidth, lineHeight, lineAscender, lineDescender, charSpacing);
        lines.emplace_back(lineText, lineWidth, lineHeight, lineAscender, lineDescender);
    }

    // 如果指定了最大高度，限制行数
    if (maxHeight > 0) {
        int currentHeight = 0;
        size_t maxLines = 0;
        for (size_t i = 0; i < lines.size(); i++) {
            currentHeight += lines[i].height;
            if (currentHeight > maxHeight) {
                break;
            }
            maxLines = i + 1;
        }
        if (maxLines < lines.size()) {
            lines.resize(maxLines);
        }
    }

    return lines;
}

// 检查单词是否需要分割
bool FontRenderer::needsWordBreaking(const std::string& word, int maxWidth, int outlineWidth, int charSpacing) {
    if (word.empty() || maxWidth <= 0) {
        return false;
    }

    int wordWidth, wordHeight, wordAscender, wordDescender;
    if (calculateTextDimensions(word, outlineWidth, wordWidth, wordHeight, wordAscender, wordDescender, charSpacing)) {
        return wordWidth > maxWidth;
    }

    return false;
}

// 分割过长的单词
void FontRenderer::breakLongWord(const std::string& word, int maxWidth, int outlineWidth, int charSpacing, std::vector<LineInfo>& lines) {
    if (word.empty()) {
        return;
    }

    std::string currentPart;

    for (size_t i = 0; i < word.length(); /* increment in loop */) {
        FT_UInt charCode = 0;
        int charLen = decodeUTF8Char(word, i, charCode);

        if (charLen == 0) {
            i++;
            continue;
        }

        // 添加当前字符
        std::string charStr;
        for (int j = 0; j < charLen; j++) {
            charStr += word[i + j];
        }

        std::string testPart = currentPart + charStr;
        int testWidth, testHeight, testAscender, testDescender;

        if (calculateTextDimensions(testPart, outlineWidth, testWidth, testHeight, testAscender, testDescender, charSpacing)) {
            if (testWidth > maxWidth && !currentPart.empty()) {
                // 超过宽度，输出当前部分
                int partWidth, partHeight, partAscender, partDescender;
                calculateTextDimensions(currentPart, outlineWidth, partWidth, partHeight, partAscender, partDescender, charSpacing);
                lines.emplace_back(currentPart, partWidth, partHeight, partAscender, partDescender);
                currentPart = charStr;
            } else {
                // 不超过宽度，继续添加
                currentPart = testPart;
            }
        } else {
            // 计算失败，直接添加
            currentPart = testPart;
        }

        i += charLen;
    }

    // 添加最后一部分
    if (!currentPart.empty()) {
        int partWidth, partHeight, partAscender, partDescender;
        calculateTextDimensions(currentPart, outlineWidth, partWidth, partHeight, partAscender, partDescender, charSpacing);
        lines.emplace_back(currentPart, partWidth, partHeight, partAscender, partDescender);
    }
}

// 将字符串数组转换为行信息
std::vector<LineInfo> FontRenderer::convertToLineInfo(const std::vector<std::string>& textLines,
                                                     int outlineWidth, int charSpacing) {
    std::vector<LineInfo> lines;
    if (!m_face) {
        return lines;
    }

    for (const std::string& line : textLines) {
        int width, height, ascender, descender;
        if (calculateTextDimensions(line, outlineWidth, width, height, ascender, descender, charSpacing)) {
            lines.emplace_back(line, width, height, ascender, descender);
        } else {
            lines.emplace_back(line, 0, 0, 0, 0);
        }
    }

    return lines;
}

// 渲染行信息列表到位图（可选最大宽度限制，-1表示不限制）
Bitmap FontRenderer::renderLineInfos(const std::vector<LineInfo>& lineInfos, AutoWrapAlignment align,
                                    const Color& color, PixelFormat format,
                                    int outlineWidth, const Color& outlineColor, int charSpacing, int maxWidth) {
    if (lineInfos.empty()) {
        return Bitmap();
    }

    // 计算总尺寸
    int actualMaxWidth = 0;
    int totalHeight = 0;

    for (const LineInfo& line : lineInfos) {
        if (maxWidth > 0) {
            // 有最大宽度限制
            actualMaxWidth = std::max(actualMaxWidth, std::min(line.width, maxWidth));
        } else {
            // 无最大宽度限制
            actualMaxWidth = std::max(actualMaxWidth, line.width);
        }
        totalHeight += line.height;
    }

    // 确定位图宽度
    int bitmapWidth = (maxWidth > 0) ? maxWidth : actualMaxWidth;

    // 创建总位图
    Bitmap bitmap(bitmapWidth, totalHeight, format);

    // 渲染每一行
    int currentY = 0;
    for (const LineInfo& lineInfo : lineInfos) {
        if (lineInfo.text.empty()) {
            currentY += lineInfo.height;
            continue;
        }

        // 渲染当前行到临时位图
        Bitmap lineBitmap = renderText(lineInfo.text, color, format, outlineWidth, outlineColor, charSpacing);

        if (lineBitmap.width == 0 || lineBitmap.height == 0) {
            currentY += lineInfo.height;
            continue;
        }

        // 计算对齐位置
        int startX = 0;
        if (align == AutoWrapAlignment::RIGHT) {
            if (maxWidth > 0) {
                startX = bitmapWidth - std::min(lineInfo.width, bitmapWidth);
            } else {
                startX = bitmapWidth - lineInfo.width;
            }
        }
        // LEFT对齐时startX保持为0

        // 将行位图复制到总位图中
        copyBitmapToTarget(lineBitmap, bitmap, startX, currentY);

        currentY += lineInfo.height;
    }

    return bitmap;
}



void FontRenderer::renderGlyphARGB8888(FT_GlyphSlot glyph, Bitmap& bitmap, int& penX, int& penY, const Color& color) {
    FT_Bitmap& ftBitmap = glyph->bitmap;

    int left = penX + glyph->bitmap_left;
    int top = penY - glyph->bitmap_top;

    // 复制字形位图到我们的位图
    for (unsigned int y = 0; y < ftBitmap.rows; y++) {
        for (unsigned int x = 0; x < ftBitmap.width; x++) {
            int srcIdx = y * ftBitmap.pitch + x;
            unsigned char alpha = ftBitmap.buffer[srcIdx];

            if (alpha > 0) {
                int dstX = left + x;
                int dstY = top + y;

                if (dstX >= 0 && dstX < bitmap.width && dstY >= 0 && dstY < bitmap.height) {
                    int dstIdx = (dstY * bitmap.width + dstX) * 4;

                    // 应用alpha混合
                    float a = alpha / 255.0f;
                    bitmap.buffer[dstIdx + 0] = static_cast<unsigned char>(color.a * a);
                    bitmap.buffer[dstIdx + 1] = static_cast<unsigned char>(color.r * a);
                    bitmap.buffer[dstIdx + 2] = static_cast<unsigned char>(color.g * a);
                    bitmap.buffer[dstIdx + 3] = static_cast<unsigned char>(color.b * a);
                }
            }
        }
    }
}

void FontRenderer::renderGlyphARGB1555(FT_GlyphSlot glyph, Bitmap& bitmap, int& penX, int& penY, const Color& color) {
    FT_Bitmap& ftBitmap = glyph->bitmap;

    int left = penX + glyph->bitmap_left;
    int top = penY - glyph->bitmap_top;

    // 直接使用调色板索引
    int colorIndex = color.paletteIndex >= 0 ? color.paletteIndex : 2;  // 默认使用白色（索引2）
    unsigned short textPixel = g_globalARGB1555Palette[colorIndex];

    // 找到一个比指定颜色稍浅的颜色索引
    // 先尝试找到一个亮度更高的颜色
    int lighterColorIndex = colorIndex;

    // 如果是默认调色板，我们可以使用预定义的映射
    if (colorIndex == 1) { // 黑色
        lighterColorIndex = 9;  // 浅灰色
    } else if (colorIndex == 2) { // 白色
        lighterColorIndex = 14;  // 浅白色
    } else if (colorIndex == 3) { // 红色
        lighterColorIndex = 8;   // 品红色
    } else if (colorIndex == 4) { // 绿色
        lighterColorIndex = 7;   // 青色
    } else if (colorIndex == 5) { // 蓝色
        lighterColorIndex = 8;   // 品红色
    } else {
        // 对于其他颜色，使用浅灰色
        lighterColorIndex = 14;  // 浅白色
    }

    unsigned short lighterPixel = g_globalARGB1555Palette[lighterColorIndex];

    // 复制字形位图到我们的位图
    for (unsigned int y = 0; y < ftBitmap.rows; y++) {
        for (unsigned int x = 0; x < ftBitmap.width; x++) {
            int srcIdx = y * ftBitmap.pitch + x;
            unsigned char alpha = ftBitmap.buffer[srcIdx];

            if (alpha > 0) {
                int dstX = left + x;
                int dstY = top + y;

                if (dstX >= 0 && dstX < bitmap.width && dstY >= 0 && dstY < bitmap.height) {
                    int dstIdx = (dstY * bitmap.width + dstX) * 2;

                    // 根据alpha值选择颜色
                    if (alpha > 112) {
                        // 完全不透明或接近不透明，使用原始颜色
                        bitmap.buffer[dstIdx] = textPixel & 0xFF;
                        bitmap.buffer[dstIdx + 1] = (textPixel >> 8) & 0xFF;
                    } else if (alpha > 1) {
                        // 半透明，使用稍浅的颜色
                        bitmap.buffer[dstIdx] = lighterPixel & 0xFF;
                        bitmap.buffer[dstIdx + 1] = (lighterPixel >> 8) & 0xFF;
                    }
                }
            }
        }
    }
}

void FontRenderer::renderGlyphI4(FT_GlyphSlot glyph, Bitmap& bitmap, int& penX, int& penY, const Color& color) {
    FT_Bitmap& ftBitmap = glyph->bitmap;

    int left = penX + glyph->bitmap_left;
    int top = penY - glyph->bitmap_top;

    // 直接使用调色板索引
    int colorIndex = color.paletteIndex >= 0 ? color.paletteIndex : 2;  // 默认使用白色（索引2）

    // 找到一个比指定颜色稍浅的颜色索引
    // 先尝试找到一个亮度更高的颜色
    int lighterColorIndex = colorIndex;

    // 如果是默认调色板，我们可以使用预定义的映射
    if (colorIndex == 1) { // 黑色
        lighterColorIndex = 9;  // 浅灰色
    } else if (colorIndex == 2) { // 白色
        lighterColorIndex = 14;  // 浅白色
    } else if (colorIndex == 3) { // 红色
        lighterColorIndex = 8;   // 品红色
    } else if (colorIndex == 4) { // 绿色
        lighterColorIndex = 7;   // 青色
    } else if (colorIndex == 5) { // 蓝色
        lighterColorIndex = 8;   // 品红色
    } else {
        // 对于其他颜色，使用浅灰色
        lighterColorIndex = 14;  // 浅白色
    }

    // 复制字形位图到我们的位图
    for (unsigned int y = 0; y < ftBitmap.rows; y++) {
        for (unsigned int x = 0; x < ftBitmap.width; x++) {
            int srcIdx = y * ftBitmap.pitch + x;
            unsigned char alpha = ftBitmap.buffer[srcIdx];

            if (alpha > 0) {  // 渲染所有非透明像素
                int dstX = left + x;
                int dstY = top + y;

                if (dstX >= 0 && dstX < bitmap.width && dstY >= 0 && dstY < bitmap.height) {
                    // 计算目标索引
                    int byteIdx = dstY * bitmap.pitch + dstX / 2;

                    // 根据alpha值选择颜色
                    int useColorIndex;
                    if (alpha > 112) {
                        // 完全不透明或接近不透明，使用原始颜色
                        useColorIndex = colorIndex;
                    } else if (alpha > 1) {
                        // 半透明，使用稍浅的颜色
                        useColorIndex = lighterColorIndex;
                    } else {
                        // alpha太小，不渲染
                        continue;
                    }

                    // 设置适当的半字节
                    if (dstX % 2 == 0) {
                        // 高半字节
                        bitmap.buffer[byteIdx] = (useColorIndex << 4) | (bitmap.buffer[byteIdx] & 0x0F);
                    } else {
                        // 低半字节
                        bitmap.buffer[byteIdx] = (bitmap.buffer[byteIdx] & 0xF0) | useColorIndex;
                    }
                }
            }
        }
    }
}

void FontRenderer::addOutline(Bitmap& bitmap, int outlineWidth, const Color& outlineColor) {
    // 根据位图格式选择不同的描边方法
    switch (bitmap.format) {
        case PixelFormat::ARGB8888:
            addOutlineARGB8888(bitmap, outlineWidth, outlineColor);
            break;
        case PixelFormat::ARGB1555:
            addOutlineARGB1555(bitmap, outlineWidth, outlineColor);
            break;
        case PixelFormat::I4:
            addOutlineI4(bitmap, outlineWidth, outlineColor);
            break;
    }
}

void FontRenderer::addOutlineARGB8888(Bitmap& bitmap, int outlineWidth, const Color& outlineColor) {
    // 创建原始位图的副本
    std::vector<unsigned char> originalBuffer = bitmap.buffer;

    // 对于原始位图中的每个像素
    for (int y = 0; y < bitmap.height; y++) {
        for (int x = 0; x < bitmap.width; x++) {
            int idx = (y * bitmap.width + x) * 4;

            // 如果这个像素的alpha > 0，它是文本的一部分
            if (originalBuffer[idx] > 0) {
                continue;  // 跳过文本像素
            }

            // 检查周围像素是否有文本
            bool isOutline = false;

            for (int dy = -outlineWidth; dy <= outlineWidth && !isOutline; dy++) {
                for (int dx = -outlineWidth; dx <= outlineWidth && !isOutline; dx++) {
                    if (dx == 0 && dy == 0) continue;

                    int nx = x + dx;
                    int ny = y + dy;

                    if (nx >= 0 && nx < bitmap.width && ny >= 0 && ny < bitmap.height) {
                        int neighborIdx = (ny * bitmap.width + nx) * 4;

                        if (originalBuffer[neighborIdx] > 0) {
                            isOutline = true;
                        }
                    }
                }
            }

            // 如果这个像素是描边的一部分，设置它的颜色
            if (isOutline) {
                bitmap.buffer[idx + 0] = outlineColor.a;
                bitmap.buffer[idx + 1] = outlineColor.r;
                bitmap.buffer[idx + 2] = outlineColor.g;
                bitmap.buffer[idx + 3] = outlineColor.b;
            }
        }
    }
}

void FontRenderer::addOutlineARGB1555(Bitmap& bitmap, int outlineWidth, const Color& outlineColor) {
    // 创建原始位图的副本
    std::vector<unsigned char> originalBuffer = bitmap.buffer;

    // 直接使用调色板索引
    int outlineColorIndex = outlineColor.paletteIndex >= 0 ? outlineColor.paletteIndex : 3;  // 默认使用红色（索引3）
    unsigned short outlinePixel = g_globalARGB1555Palette[outlineColorIndex];

    // 对于原始位图中的每个像素
    for (int y = 0; y < bitmap.height; y++) {
        for (int x = 0; x < bitmap.width; x++) {
            int idx = (y * bitmap.width + x) * 2;

            // 读取ARGB1555像素（小端序）
            unsigned short pixel = static_cast<unsigned short>(originalBuffer[idx]) |
                                  (static_cast<unsigned short>(originalBuffer[idx + 1]) << 8);

            // 如果这个像素的alpha > 0，它是文本的一部分
            if (pixel & 0x8000) {
                continue;  // 跳过文本像素
            }

            // 检查周围像素是否有文本
            bool isOutline = false;

            for (int dy = -outlineWidth; dy <= outlineWidth && !isOutline; dy++) {
                for (int dx = -outlineWidth; dx <= outlineWidth && !isOutline; dx++) {
                    if (dx == 0 && dy == 0) continue;

                    int nx = x + dx;
                    int ny = y + dy;

                    if (nx >= 0 && nx < bitmap.width && ny >= 0 && ny < bitmap.height) {
                        int neighborIdx = (ny * bitmap.width + nx) * 2;
                        unsigned short neighborPixel = static_cast<unsigned short>(originalBuffer[neighborIdx]) |
                                                     (static_cast<unsigned short>(originalBuffer[neighborIdx + 1]) << 8);

                        if (neighborPixel & 0x8000) {
                            isOutline = true;
                        }
                    }
                }
            }

            // 如果这个像素是描边的一部分，设置它的颜色
            if (isOutline) {
                bitmap.buffer[idx] = outlinePixel & 0xFF;
                bitmap.buffer[idx + 1] = (outlinePixel >> 8) & 0xFF;
            }
        }
    }
}

void FontRenderer::addOutlineI4(Bitmap& bitmap, int outlineWidth, const Color& outlineColor) {
    // 创建原始位图的副本
    std::vector<unsigned char> originalBuffer = bitmap.buffer;

    // 直接使用调色板索引
    int outlineColorIdx = outlineColor.paletteIndex >= 0 ? outlineColor.paletteIndex : 3;  // 默认使用红色（索引3）

    // 对于原始位图中的每个像素
    for (int y = 0; y < bitmap.height; y++) {
        for (int x = 0; x < bitmap.width; x++) {
            int byteIdx = y * bitmap.pitch + x / 2;
            unsigned char paletteIdx;

            // 提取调色板索引
            if (x % 2 == 0) {
                // 高半字节
                paletteIdx = (originalBuffer[byteIdx] >> 4) & 0x0F;
            } else {
                // 低半字节
                paletteIdx = originalBuffer[byteIdx] & 0x0F;
            }

            // 如果这个像素有颜色（不是完全透明），它是文本的一部分
            if (g_globalPalette[paletteIdx].a > 0) {
                continue;  // 跳过文本像素
            }

            // 检查周围像素是否有文本
            bool isOutline = false;

            for (int dy = -outlineWidth; dy <= outlineWidth && !isOutline; dy++) {
                for (int dx = -outlineWidth; dx <= outlineWidth && !isOutline; dx++) {
                    if (dx == 0 && dy == 0) continue;

                    int nx = x + dx;
                    int ny = y + dy;

                    if (nx >= 0 && nx < bitmap.width && ny >= 0 && ny < bitmap.height) {
                        int neighborByteIdx = ny * bitmap.pitch + nx / 2;
                        unsigned char neighborPaletteIdx;

                        if (nx % 2 == 0) {
                            neighborPaletteIdx = (originalBuffer[neighborByteIdx] >> 4) & 0x0F;
                        } else {
                            neighborPaletteIdx = originalBuffer[neighborByteIdx] & 0x0F;
                        }

                        if (g_globalPalette[neighborPaletteIdx].a > 0) {
                            isOutline = true;
                        }
                    }
                }
            }

            // 如果这个像素是描边的一部分，设置它的颜色
            if (isOutline) {
                if (x % 2 == 0) {
                    // 高半字节
                    bitmap.buffer[byteIdx] = (outlineColorIdx << 4) | (bitmap.buffer[byteIdx] & 0x0F);
                } else {
                    // 低半字节
                    bitmap.buffer[byteIdx] = (bitmap.buffer[byteIdx] & 0xF0) | outlineColorIdx;
                }
            }
        }
    }
}

// 性能优化方法实现

// 获取字形信息（带缓存）
const GlyphInfo& FontRenderer::getGlyphInfo(FT_UInt charCode) {
    auto it = m_glyphCache.find(charCode);
    if (it != m_glyphCache.end()) {
        return it->second;
    }

    // 缓存中没有，需要加载字形
    FT_UInt glyphIndex = FT_Get_Char_Index(m_face, charCode);
    FT_Error error = FT_Load_Glyph(m_face, glyphIndex, FT_LOAD_DEFAULT);

    GlyphInfo info;
    if (!error) {
        info.advance = m_face->glyph->advance.x >> 6;

        // 渲染字形以获得正确的位图信息
        FT_Error renderError = FT_Render_Glyph(m_face->glyph, FT_RENDER_MODE_NORMAL);
        if (!renderError) {
            info.width = m_face->glyph->bitmap.width;
            info.height = m_face->glyph->bitmap.rows;
            info.bearingX = m_face->glyph->bitmap_left;
            info.bearingY = m_face->glyph->bitmap_top;
        } else {
            // 渲染失败，使用字形度量信息
            info.width = (m_face->glyph->metrics.width >> 6);
            info.height = (m_face->glyph->metrics.height >> 6);
            info.bearingX = (m_face->glyph->metrics.horiBearingX >> 6);
            info.bearingY = (m_face->glyph->metrics.horiBearingY >> 6);
        }

        info.isCombining = isCombiningCharacter(charCode);
    }

    // 将结果放入缓存
    m_glyphCache[charCode] = info;
    return m_glyphCache[charCode];
}

// 清空缓存
void FontRenderer::clearCache() {
    m_glyphCache.clear();
    m_dimensionCache.clear();
}

// 生成缓存键
std::string FontRenderer::generateCacheKey(const std::string& text, int outlineWidth, int charSpacing) const {
    return text + "_" + std::to_string(outlineWidth) + "_" + std::to_string(charSpacing);
}


