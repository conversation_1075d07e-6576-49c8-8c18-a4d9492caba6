#include "font_renderer.h"
#include <iostream>
#include <cstring>
#include <algorithm>
#include <cmath>
#include <limits>

FontRenderer::FontRenderer() : m_library(nullptr), m_face(nullptr), m_initialized(false) {
    // 初始化默认调色板（灰度）
    for (int i = 0; i < PALETTE_SIZE; i++) {
        unsigned char value = static_cast<unsigned char>((i * 255) / (PALETTE_SIZE - 1));
        m_palette[i] = Color(255, value, value, value);
    }
}

FontRenderer::~FontRenderer() {
    if (m_face) {
        FT_Done_Face(m_face);
    }

    if (m_initialized) {
        FT_Done_FreeType(m_library);
    }
}

bool FontRenderer::initialize() {
    FT_Error error = FT_Init_FreeType(&m_library);
    if (error) {
        std::cerr << "Error initializing FreeType library" << std::endl;
        return false;
    }

    m_initialized = true;
    return true;
}

bool FontRenderer::loadFont(const std::string& fontPath, int fontSize) {
    if (!m_initialized) {
        std::cerr << "FreeType library not initialized" << std::endl;
        return false;
    }

    // If a face was previously loaded, clean it up
    if (m_face) {
        FT_Done_Face(m_face);
        m_face = nullptr;
    }

    FT_Error error = FT_New_Face(m_library, fontPath.c_str(), 0, &m_face);
    if (error) {
        std::cerr << "Error loading font: " << fontPath << std::endl;
        return false;
    }

    // Set font size
    error = FT_Set_Pixel_Sizes(m_face, 0, fontSize);
    if (error) {
        std::cerr << "Error setting font size" << std::endl;
        FT_Done_Face(m_face);
        m_face = nullptr;
        return false;
    }

    return true;
}

int FontRenderer::decodeUTF8Char(const std::string& text, size_t index, FT_UInt& charCode) {
    // 初始化字符码和字符长度
    charCode = 0;
    int charLen = 0;

    // 处理UTF-8编码
    if ((text[index] & 0x80) == 0) {
        // 1字节字符
        charCode = text[index];
        charLen = 1;
    } else if ((text[index] & 0xE0) == 0xC0) {
        // 2字节字符
        if (index + 1 < text.length()) {
            charCode = ((text[index] & 0x1F) << 6) | (text[index+1] & 0x3F);
            charLen = 2;
        }
    } else if ((text[index] & 0xF0) == 0xE0) {
        // 3字节字符
        if (index + 2 < text.length()) {
            charCode = ((text[index] & 0x0F) << 12) | ((text[index+1] & 0x3F) << 6) | (text[index+2] & 0x3F);
            charLen = 3;
        }
    } else if ((text[index] & 0xF8) == 0xF0) {
        // 4字节字符
        if (index + 3 < text.length()) {
            charCode = ((text[index] & 0x07) << 18) | ((text[index+1] & 0x3F) << 12) |
                       ((text[index+2] & 0x3F) << 6) | (text[index+3] & 0x3F);
            charLen = 4;
        }
    }

    return charLen;
}

bool FontRenderer::isCombiningCharacter(FT_UInt charCode) {
    // 泰文组合字符范围
    // 上标符号（声调符号）: U+0E31, U+0E34-U+0E3A
    // 下标符号: U+0E47-U+0E4E
    if ((charCode >= 0x0E31 && charCode <= 0x0E3A) ||
        (charCode >= 0x0E47 && charCode <= 0x0E4E) ||
        charCode == 0x0E31) {
        return true;
    }

    // 其他语言的组合字符范围
    // 阿拉伯文组合字符: U+064B-U+065F, U+0670, U+06D6-U+06ED
    if ((charCode >= 0x064B && charCode <= 0x065F) ||
        charCode == 0x0670 ||
        (charCode >= 0x06D6 && charCode <= 0x06ED)) {
        return true;
    }

    // 印度文组合字符: U+0900-U+097F范围内的一些字符
    if (charCode >= 0x0900 && charCode <= 0x097F) {
        // 简化处理，将大部分印度文组合字符视为组合字符
        if ((charCode >= 0x0901 && charCode <= 0x0903) ||
            (charCode >= 0x093C && charCode <= 0x094F) ||
            (charCode >= 0x0951 && charCode <= 0x0957) ||
            (charCode >= 0x0962 && charCode <= 0x0963)) {
            return true;
        }
    }

    // Unicode组合字符类别（简化处理）
    // 组合附加符号: U+0300-U+036F
    if (charCode >= 0x0300 && charCode <= 0x036F) {
        return true;
    }

    return false;
}

bool FontRenderer::calculateTextDimensions(const std::string& text, int outlineWidth, int& totalWidth, int& totalHeight, int& maxAscender, int& maxDescender, int charSpacing) {
    if (!m_face) {
        std::cerr << "No font loaded" << std::endl;
        return false;
    }

    // 初始化参数
    totalWidth = 0;
    int maxHeight = 0;
    maxAscender = 0;
    maxDescender = 0;

    for (size_t i = 0; i < text.length(); /* increment in loop */) {
        FT_UInt charCode = 0;
        int charLen = decodeUTF8Char(text, i, charCode);

        if (charLen == 0) {
            // 无效的UTF-8序列
            i++;
            continue;
        }

        // 获取字形索引
        FT_UInt glyphIndex = FT_Get_Char_Index(m_face, charCode);

        // 加载字形
        FT_Error error = FT_Load_Glyph(m_face, glyphIndex, FT_LOAD_DEFAULT);
        if (error) {
            i += charLen;
            continue;
        }

        // 渲染字形
        error = FT_Render_Glyph(m_face->glyph, FT_RENDER_MODE_NORMAL);
        if (error) {
            i += charLen;
            continue;
        }

        int advance = m_face->glyph->advance.x >> 6;
        totalWidth += advance;

        // 只在非组合字符之间添加间距
        if (!isCombiningCharacter(charCode)) {
            totalWidth += charSpacing;
        }

        maxHeight = std::max(maxHeight, static_cast<int>(m_face->glyph->bitmap.rows));
        maxAscender = std::max(maxAscender, m_face->glyph->bitmap_top);
        maxDescender = std::max(maxDescender, static_cast<int>(m_face->glyph->bitmap.rows) - m_face->glyph->bitmap_top);

        i += charLen;
    }

    // 为描边添加空间
    totalWidth += outlineWidth * 2;
    totalHeight = maxAscender + maxDescender + outlineWidth * 2;

    return true;
}

bool FontRenderer::getTextDimensions(const std::string& text, int outlineWidth, int& width, int& height, int charSpacing) {
    int totalWidth, totalHeight, maxAscender, maxDescender;

    // 使用辅助方法计算文本尺寸
    if (!calculateTextDimensions(text, outlineWidth, totalWidth, totalHeight, maxAscender, maxDescender, charSpacing)) {
        return false;
    }

    // 设置输出参数
    width = totalWidth;
    height = totalHeight;

    return true;
}

Bitmap FontRenderer::renderText(const std::string& text, const Color& color,
                               PixelFormat format, int outlineWidth,
                               const Color& outlineColor, int charSpacing) {
    if (!m_face) {
        std::cerr << "No font loaded" << std::endl;
        return Bitmap();
    }

    // 使用全局调色板
    m_palette = g_globalPalette;

    // 使用辅助方法计算文本尺寸
    int totalWidth, totalHeight, maxAscender, maxDescender;
    if (!calculateTextDimensions(text, outlineWidth, totalWidth, totalHeight, maxAscender, maxDescender, charSpacing)) {
        return Bitmap();
    }

    // 创建指定格式的位图
    Bitmap bitmap(totalWidth, totalHeight, format);

    // 第二遍：渲染文本到位图
    int penX = outlineWidth;
    int penY = maxAscender + outlineWidth;

    for (size_t i = 0; i < text.length(); /* increment in loop */) {
        FT_UInt charCode = 0;
        int charLen = decodeUTF8Char(text, i, charCode);

        if (charLen == 0) {
            i++;
            continue;
        }

        FT_UInt glyphIndex = FT_Get_Char_Index(m_face, charCode);
        FT_Error error = FT_Load_Glyph(m_face, glyphIndex, FT_LOAD_DEFAULT);
        if (error) {
            i += charLen;
            continue;
        }

        error = FT_Render_Glyph(m_face->glyph, FT_RENDER_MODE_NORMAL);
        if (error) {
            i += charLen;
            continue;
        }

        // 根据位图格式选择不同的渲染方法
        switch (format) {
            case PixelFormat::ARGB8888:
                renderGlyphARGB8888(m_face->glyph, bitmap, penX, penY, color);
                break;
            case PixelFormat::ARGB1555:
                renderGlyphARGB1555(m_face->glyph, bitmap, penX, penY, color);
                break;
            case PixelFormat::I4:
                renderGlyphI4(m_face->glyph, bitmap, penX, penY, color);
                break;
        }

        // 移动笔位置，只在非组合字符之间添加间距
        int advance = m_face->glyph->advance.x >> 6;
        penX += advance;

        // 只在非组合字符之间添加间距
        if (!isCombiningCharacter(charCode)) {
            penX += charSpacing;
        }

        i += charLen;
    }

    // 添加描边
    if (outlineWidth > 0) {
        addOutline(bitmap, outlineWidth, outlineColor);
    }

    return bitmap;
}

void FontRenderer::renderGlyphARGB8888(FT_GlyphSlot glyph, Bitmap& bitmap, int& penX, int& penY, const Color& color) {
    FT_Bitmap& ftBitmap = glyph->bitmap;

    int left = penX + glyph->bitmap_left;
    int top = penY - glyph->bitmap_top;

    // 复制字形位图到我们的位图
    for (unsigned int y = 0; y < ftBitmap.rows; y++) {
        for (unsigned int x = 0; x < ftBitmap.width; x++) {
            int srcIdx = y * ftBitmap.pitch + x;
            unsigned char alpha = ftBitmap.buffer[srcIdx];

            if (alpha > 0) {
                int dstX = left + x;
                int dstY = top + y;

                if (dstX >= 0 && dstX < bitmap.width && dstY >= 0 && dstY < bitmap.height) {
                    int dstIdx = (dstY * bitmap.width + dstX) * 4;

                    // 应用alpha混合
                    float a = alpha / 255.0f;
                    bitmap.buffer[dstIdx + 0] = static_cast<unsigned char>(color.a * a);
                    bitmap.buffer[dstIdx + 1] = static_cast<unsigned char>(color.r * a);
                    bitmap.buffer[dstIdx + 2] = static_cast<unsigned char>(color.g * a);
                    bitmap.buffer[dstIdx + 3] = static_cast<unsigned char>(color.b * a);
                }
            }
        }
    }
}

void FontRenderer::renderGlyphARGB1555(FT_GlyphSlot glyph, Bitmap& bitmap, int& penX, int& penY, const Color& color) {
    FT_Bitmap& ftBitmap = glyph->bitmap;

    int left = penX + glyph->bitmap_left;
    int top = penY - glyph->bitmap_top;

    // 直接使用调色板索引
    int colorIndex = color.paletteIndex >= 0 ? color.paletteIndex : 2;  // 默认使用白色（索引2）
    unsigned short textPixel = g_globalARGB1555Palette[colorIndex];

    // 找到一个比指定颜色稍浅的颜色索引
    // 先尝试找到一个亮度更高的颜色
    int lighterColorIndex = colorIndex;

    // 如果是默认调色板，我们可以使用预定义的映射
    if (colorIndex == 1) { // 黑色
        lighterColorIndex = 9;  // 浅灰色
    } else if (colorIndex == 2) { // 白色
        lighterColorIndex = 14;  // 浅白色
    } else if (colorIndex == 3) { // 红色
        lighterColorIndex = 8;   // 品红色
    } else if (colorIndex == 4) { // 绿色
        lighterColorIndex = 7;   // 青色
    } else if (colorIndex == 5) { // 蓝色
        lighterColorIndex = 8;   // 品红色
    } else {
        // 对于其他颜色，使用浅灰色
        lighterColorIndex = 14;  // 浅白色
    }

    unsigned short lighterPixel = g_globalARGB1555Palette[lighterColorIndex];

    // 复制字形位图到我们的位图
    for (unsigned int y = 0; y < ftBitmap.rows; y++) {
        for (unsigned int x = 0; x < ftBitmap.width; x++) {
            int srcIdx = y * ftBitmap.pitch + x;
            unsigned char alpha = ftBitmap.buffer[srcIdx];

            if (alpha > 0) {
                int dstX = left + x;
                int dstY = top + y;

                if (dstX >= 0 && dstX < bitmap.width && dstY >= 0 && dstY < bitmap.height) {
                    int dstIdx = (dstY * bitmap.width + dstX) * 2;

                    // 根据alpha值选择颜色
                    if (alpha > 112) {
                        // 完全不透明或接近不透明，使用原始颜色
                        bitmap.buffer[dstIdx] = textPixel & 0xFF;
                        bitmap.buffer[dstIdx + 1] = (textPixel >> 8) & 0xFF;
                    } else if (alpha > 1) {
                        // 半透明，使用稍浅的颜色
                        bitmap.buffer[dstIdx] = lighterPixel & 0xFF;
                        bitmap.buffer[dstIdx + 1] = (lighterPixel >> 8) & 0xFF;
                    }
                }
            }
        }
    }
}

void FontRenderer::renderGlyphI4(FT_GlyphSlot glyph, Bitmap& bitmap, int& penX, int& penY, const Color& color) {
    FT_Bitmap& ftBitmap = glyph->bitmap;

    int left = penX + glyph->bitmap_left;
    int top = penY - glyph->bitmap_top;

    // 直接使用调色板索引
    int colorIndex = color.paletteIndex >= 0 ? color.paletteIndex : 2;  // 默认使用白色（索引2）

    // 找到一个比指定颜色稍浅的颜色索引
    // 先尝试找到一个亮度更高的颜色
    int lighterColorIndex = colorIndex;

    // 如果是默认调色板，我们可以使用预定义的映射
    if (colorIndex == 1) { // 黑色
        lighterColorIndex = 9;  // 浅灰色
    } else if (colorIndex == 2) { // 白色
        lighterColorIndex = 14;  // 浅白色
    } else if (colorIndex == 3) { // 红色
        lighterColorIndex = 8;   // 品红色
    } else if (colorIndex == 4) { // 绿色
        lighterColorIndex = 7;   // 青色
    } else if (colorIndex == 5) { // 蓝色
        lighterColorIndex = 8;   // 品红色
    } else {
        // 对于其他颜色，使用浅灰色
        lighterColorIndex = 14;  // 浅白色
    }

    // 复制字形位图到我们的位图
    for (unsigned int y = 0; y < ftBitmap.rows; y++) {
        for (unsigned int x = 0; x < ftBitmap.width; x++) {
            int srcIdx = y * ftBitmap.pitch + x;
            unsigned char alpha = ftBitmap.buffer[srcIdx];

            if (alpha > 0) {  // 渲染所有非透明像素
                int dstX = left + x;
                int dstY = top + y;

                if (dstX >= 0 && dstX < bitmap.width && dstY >= 0 && dstY < bitmap.height) {
                    // 计算目标索引
                    int byteIdx = dstY * bitmap.pitch + dstX / 2;

                    // 根据alpha值选择颜色
                    int useColorIndex;
                    if (alpha > 112) {
                        // 完全不透明或接近不透明，使用原始颜色
                        useColorIndex = colorIndex;
                    } else if (alpha > 1) {
                        // 半透明，使用稍浅的颜色
                        useColorIndex = lighterColorIndex;
                    } else {
                        // alpha太小，不渲染
                        continue;
                    }

                    // 设置适当的半字节
                    if (dstX % 2 == 0) {
                        // 高半字节
                        bitmap.buffer[byteIdx] = (useColorIndex << 4) | (bitmap.buffer[byteIdx] & 0x0F);
                    } else {
                        // 低半字节
                        bitmap.buffer[byteIdx] = (bitmap.buffer[byteIdx] & 0xF0) | useColorIndex;
                    }
                }
            }
        }
    }
}

void FontRenderer::addOutline(Bitmap& bitmap, int outlineWidth, const Color& outlineColor) {
    // 根据位图格式选择不同的描边方法
    switch (bitmap.format) {
        case PixelFormat::ARGB8888:
            addOutlineARGB8888(bitmap, outlineWidth, outlineColor);
            break;
        case PixelFormat::ARGB1555:
            addOutlineARGB1555(bitmap, outlineWidth, outlineColor);
            break;
        case PixelFormat::I4:
            addOutlineI4(bitmap, outlineWidth, outlineColor);
            break;
    }
}

void FontRenderer::addOutlineARGB8888(Bitmap& bitmap, int outlineWidth, const Color& outlineColor) {
    // 创建原始位图的副本
    std::vector<unsigned char> originalBuffer = bitmap.buffer;

    // 对于原始位图中的每个像素
    for (int y = 0; y < bitmap.height; y++) {
        for (int x = 0; x < bitmap.width; x++) {
            int idx = (y * bitmap.width + x) * 4;

            // 如果这个像素的alpha > 0，它是文本的一部分
            if (originalBuffer[idx] > 0) {
                continue;  // 跳过文本像素
            }

            // 检查周围像素是否有文本
            bool isOutline = false;

            for (int dy = -outlineWidth; dy <= outlineWidth && !isOutline; dy++) {
                for (int dx = -outlineWidth; dx <= outlineWidth && !isOutline; dx++) {
                    if (dx == 0 && dy == 0) continue;

                    int nx = x + dx;
                    int ny = y + dy;

                    if (nx >= 0 && nx < bitmap.width && ny >= 0 && ny < bitmap.height) {
                        int neighborIdx = (ny * bitmap.width + nx) * 4;

                        if (originalBuffer[neighborIdx] > 0) {
                            isOutline = true;
                        }
                    }
                }
            }

            // 如果这个像素是描边的一部分，设置它的颜色
            if (isOutline) {
                bitmap.buffer[idx + 0] = outlineColor.a;
                bitmap.buffer[idx + 1] = outlineColor.r;
                bitmap.buffer[idx + 2] = outlineColor.g;
                bitmap.buffer[idx + 3] = outlineColor.b;
            }
        }
    }
}

void FontRenderer::addOutlineARGB1555(Bitmap& bitmap, int outlineWidth, const Color& outlineColor) {
    // 创建原始位图的副本
    std::vector<unsigned char> originalBuffer = bitmap.buffer;

    // 直接使用调色板索引
    int outlineColorIndex = outlineColor.paletteIndex >= 0 ? outlineColor.paletteIndex : 3;  // 默认使用红色（索引3）
    unsigned short outlinePixel = g_globalARGB1555Palette[outlineColorIndex];

    // 对于原始位图中的每个像素
    for (int y = 0; y < bitmap.height; y++) {
        for (int x = 0; x < bitmap.width; x++) {
            int idx = (y * bitmap.width + x) * 2;

            // 读取ARGB1555像素（小端序）
            unsigned short pixel = static_cast<unsigned short>(originalBuffer[idx]) |
                                  (static_cast<unsigned short>(originalBuffer[idx + 1]) << 8);

            // 如果这个像素的alpha > 0，它是文本的一部分
            if (pixel & 0x8000) {
                continue;  // 跳过文本像素
            }

            // 检查周围像素是否有文本
            bool isOutline = false;

            for (int dy = -outlineWidth; dy <= outlineWidth && !isOutline; dy++) {
                for (int dx = -outlineWidth; dx <= outlineWidth && !isOutline; dx++) {
                    if (dx == 0 && dy == 0) continue;

                    int nx = x + dx;
                    int ny = y + dy;

                    if (nx >= 0 && nx < bitmap.width && ny >= 0 && ny < bitmap.height) {
                        int neighborIdx = (ny * bitmap.width + nx) * 2;
                        unsigned short neighborPixel = static_cast<unsigned short>(originalBuffer[neighborIdx]) |
                                                     (static_cast<unsigned short>(originalBuffer[neighborIdx + 1]) << 8);

                        if (neighborPixel & 0x8000) {
                            isOutline = true;
                        }
                    }
                }
            }

            // 如果这个像素是描边的一部分，设置它的颜色
            if (isOutline) {
                bitmap.buffer[idx] = outlinePixel & 0xFF;
                bitmap.buffer[idx + 1] = (outlinePixel >> 8) & 0xFF;
            }
        }
    }
}

void FontRenderer::addOutlineI4(Bitmap& bitmap, int outlineWidth, const Color& outlineColor) {
    // 创建原始位图的副本
    std::vector<unsigned char> originalBuffer = bitmap.buffer;

    // 直接使用调色板索引
    int outlineColorIdx = outlineColor.paletteIndex >= 0 ? outlineColor.paletteIndex : 3;  // 默认使用红色（索引3）

    // 对于原始位图中的每个像素
    for (int y = 0; y < bitmap.height; y++) {
        for (int x = 0; x < bitmap.width; x++) {
            int byteIdx = y * bitmap.pitch + x / 2;
            unsigned char paletteIdx;

            // 提取调色板索引
            if (x % 2 == 0) {
                // 高半字节
                paletteIdx = (originalBuffer[byteIdx] >> 4) & 0x0F;
            } else {
                // 低半字节
                paletteIdx = originalBuffer[byteIdx] & 0x0F;
            }

            // 如果这个像素有颜色（不是完全透明），它是文本的一部分
            if (g_globalPalette[paletteIdx].a > 0) {
                continue;  // 跳过文本像素
            }

            // 检查周围像素是否有文本
            bool isOutline = false;

            for (int dy = -outlineWidth; dy <= outlineWidth && !isOutline; dy++) {
                for (int dx = -outlineWidth; dx <= outlineWidth && !isOutline; dx++) {
                    if (dx == 0 && dy == 0) continue;

                    int nx = x + dx;
                    int ny = y + dy;

                    if (nx >= 0 && nx < bitmap.width && ny >= 0 && ny < bitmap.height) {
                        int neighborByteIdx = ny * bitmap.pitch + nx / 2;
                        unsigned char neighborPaletteIdx;

                        if (nx % 2 == 0) {
                            neighborPaletteIdx = (originalBuffer[neighborByteIdx] >> 4) & 0x0F;
                        } else {
                            neighborPaletteIdx = originalBuffer[neighborByteIdx] & 0x0F;
                        }

                        if (g_globalPalette[neighborPaletteIdx].a > 0) {
                            isOutline = true;
                        }
                    }
                }
            }

            // 如果这个像素是描边的一部分，设置它的颜色
            if (isOutline) {
                if (x % 2 == 0) {
                    // 高半字节
                    bitmap.buffer[byteIdx] = (outlineColorIdx << 4) | (bitmap.buffer[byteIdx] & 0x0F);
                } else {
                    // 低半字节
                    bitmap.buffer[byteIdx] = (bitmap.buffer[byteIdx] & 0xF0) | outlineColorIdx;
                }
            }
        }
    }
}


