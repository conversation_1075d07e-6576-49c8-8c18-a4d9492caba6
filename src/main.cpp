#include "font_renderer.h"
#include "bitmap_converter.h"
#include "command_line.h"
#include "pixel_format.h"
#include "bin2bmp.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <array>
#include <vector>

// 测试新功能的函数
void runTests(FontRenderer& fontRenderer) {
    std::cout << "\n=== Running Tests for New Features ===\n" << std::endl;

    // 初始化全局调色板
    initGlobalPalette();

    // 测试1: 自动换行功能（英文）
    std::cout << "Test 1: Auto-wrap English text" << std::endl;
    std::string englishText = "This is a long English sentence that should be automatically wrapped when it exceeds the specified width.";

    Bitmap autoWrapBitmap = fontRenderer.renderText(
        englishText,
        AutoWrapAlignment::LEFT,
        200,  // max_width
        -1,   // max_height (-1 means no limit)
        Color::fromIndex(2),  // white text
        PixelFormat::ARGB1555,
        1,    // outline width
        Color::fromIndex(3),  // red outline
        2     // char spacing
    );

    if (autoWrapBitmap.width > 0 && autoWrapBitmap.height > 0) {
        std::cout << "  ✓ Auto-wrap English: " << autoWrapBitmap.width << "x" << autoWrapBitmap.height << " pixels" << std::endl;

        // 保存测试结果
        std::ofstream file1("test_autowrap_english.bin", std::ios::binary);
        if (file1.is_open()) {
            file1.write(reinterpret_cast<const char*>(autoWrapBitmap.buffer.data()), autoWrapBitmap.buffer.size());
            file1.close();

            // 同时生成BMP
            BinToBMP converter;
            converter.convert("test_autowrap_english.bin", "test_autowrap_english.bmp",
                            autoWrapBitmap.width, autoWrapBitmap.height, PixelFormat::ARGB1555, "");
        }
    } else {
        std::cout << "  ✗ Auto-wrap English failed" << std::endl;
    }

    // 测试2: 自动换行功能（中英文混合）
    std::cout << "\nTest 2: Auto-wrap Chinese-English mixed text" << std::endl;
    std::string mixedText = "这是一个包含中文和English words的混合文本，应该能够正确地进行自动换行处理。";

    Bitmap mixedWrapBitmap = fontRenderer.renderText(
        mixedText,
        AutoWrapAlignment::LEFT,
        180,  // max_width
        -1,   // max_height
        Color::fromIndex(2),  // white text
        PixelFormat::ARGB1555,
        1,    // outline width
        Color::fromIndex(3),  // red outline
        1     // char spacing
    );

    if (mixedWrapBitmap.width > 0 && mixedWrapBitmap.height > 0) {
        std::cout << "  ✓ Auto-wrap Mixed: " << mixedWrapBitmap.width << "x" << mixedWrapBitmap.height << " pixels" << std::endl;

        std::ofstream file2("test_autowrap_mixed.bin", std::ios::binary);
        if (file2.is_open()) {
            file2.write(reinterpret_cast<const char*>(mixedWrapBitmap.buffer.data()), mixedWrapBitmap.buffer.size());
            file2.close();

            BinToBMP converter;
            converter.convert("test_autowrap_mixed.bin", "test_autowrap_mixed.bmp",
                            mixedWrapBitmap.width, mixedWrapBitmap.height, PixelFormat::ARGB1555, "");
        }
    } else {
        std::cout << "  ✗ Auto-wrap Mixed failed" << std::endl;
    }

    // 测试3: 字符串数组渲染（左对齐）
    std::cout << "\nTest 3: String array rendering (LEFT alignment)" << std::endl;
    std::vector<std::string> textLines = {
        "Line 1: Short",
        "Line 2: This is a longer line",
        "Line 3: 中文测试行",
        "Line 4: Mixed 中英文 content",
        "Line 5: Final line"
    };

    Bitmap arrayLeftBitmap = fontRenderer.renderText(
        textLines,
        AutoWrapAlignment::LEFT,
        Color::fromIndex(2),  // white text
        PixelFormat::ARGB1555,
        1,    // outline width
        Color::fromIndex(3),  // red outline
        1     // char spacing
    );

    if (arrayLeftBitmap.width > 0 && arrayLeftBitmap.height > 0) {
        std::cout << "  ✓ Array LEFT: " << arrayLeftBitmap.width << "x" << arrayLeftBitmap.height << " pixels" << std::endl;

        std::ofstream file3("test_array_left.bin", std::ios::binary);
        if (file3.is_open()) {
            file3.write(reinterpret_cast<const char*>(arrayLeftBitmap.buffer.data()), arrayLeftBitmap.buffer.size());
            file3.close();

            BinToBMP converter;
            converter.convert("test_array_left.bin", "test_array_left.bmp",
                            arrayLeftBitmap.width, arrayLeftBitmap.height, PixelFormat::ARGB1555, "");
        }
    } else {
        std::cout << "  ✗ Array LEFT failed" << std::endl;
    }

    // 测试4: 字符串数组渲染（右对齐）
    std::cout << "\nTest 4: String array rendering (RIGHT alignment)" << std::endl;

    Bitmap arrayRightBitmap = fontRenderer.renderText(
        textLines,
        AutoWrapAlignment::RIGHT,
        Color::fromIndex(2),  // white text
        PixelFormat::ARGB1555,
        1,    // outline width
        Color::fromIndex(3),  // red outline
        1     // char spacing
    );

    if (arrayRightBitmap.width > 0 && arrayRightBitmap.height > 0) {
        std::cout << "  ✓ Array RIGHT: " << arrayRightBitmap.width << "x" << arrayRightBitmap.height << " pixels" << std::endl;

        std::ofstream file4("test_array_right.bin", std::ios::binary);
        if (file4.is_open()) {
            file4.write(reinterpret_cast<const char*>(arrayRightBitmap.buffer.data()), arrayRightBitmap.buffer.size());
            file4.close();

            BinToBMP converter;
            converter.convert("test_array_right.bin", "test_array_right.bmp",
                            arrayRightBitmap.width, arrayRightBitmap.height, PixelFormat::ARGB1555, "");
        }
    } else {
        std::cout << "  ✗ Array RIGHT failed" << std::endl;
    }

    // 测试5: I4格式的自动换行
    std::cout << "\nTest 5: Auto-wrap with I4 format" << std::endl;
    std::string i4Text = "这是I4格式的自动换行测试，包含中文字符。";

    Bitmap i4WrapBitmap = fontRenderer.renderText(
        i4Text,
        AutoWrapAlignment::LEFT,
        150,  // max_width
        -1,   // max_height
        Color::fromIndex(2),  // white text
        PixelFormat::I4,
        1,    // outline width
        Color::fromIndex(3),  // red outline
        0     // char spacing
    );

    if (i4WrapBitmap.width > 0 && i4WrapBitmap.height > 0) {
        std::cout << "  ✓ I4 Auto-wrap: " << i4WrapBitmap.width << "x" << i4WrapBitmap.height << " pixels" << std::endl;

        std::ofstream file5("test_i4_autowrap.bin", std::ios::binary);
        if (file5.is_open()) {
            file5.write(reinterpret_cast<const char*>(i4WrapBitmap.buffer.data()), i4WrapBitmap.buffer.size());
            file5.close();

            BinToBMP converter;
            converter.convert("test_i4_autowrap.bin", "test_i4_autowrap.bmp",
                            i4WrapBitmap.width, i4WrapBitmap.height, PixelFormat::I4, "");
        }
    } else {
        std::cout << "  ✗ I4 Auto-wrap failed" << std::endl;
    }

    // 测试6: 泰文组合字符测试
    std::cout << "\nTest 6: Thai combining characters with spacing" << std::endl;
    std::string thaiText = "ที่ยนื่อตั้ต้เพื่กุมูญฎฏฐปิป๊ฟีฝื่ก่าเกแจโอแตะโดะก๐๑๒๓๔๕๖๗๘๙งนมกดบ";

    Bitmap thaiBitmap = fontRenderer.renderText(
        thaiText,
        Color::fromIndex(2),  // white text
        PixelFormat::ARGB1555,
        1,    // outline width
        Color::fromIndex(3),  // red outline
        3     // char spacing
    );

    if (thaiBitmap.width > 0 && thaiBitmap.height > 0) {
        std::cout << "  ✓ Thai combining: " << thaiBitmap.width << "x" << thaiBitmap.height << " pixels" << std::endl;

        std::ofstream file6("test_thai_combining.bin", std::ios::binary);
        if (file6.is_open()) {
            file6.write(reinterpret_cast<const char*>(thaiBitmap.buffer.data()), thaiBitmap.buffer.size());
            file6.close();

            BinToBMP converter;
            converter.convert("test_thai_combining.bin", "test_thai_combining.bmp",
                            thaiBitmap.width, thaiBitmap.height, PixelFormat::ARGB1555, "");
        }
    } else {
        std::cout << "  ✗ Thai combining failed" << std::endl;
    }

    // 测试7: 简化版换行（英文）
    std::cout << "\nTest 7: Simple wrap English text" << std::endl;
    std::string simpleEnglishText = "This is a long English sentence that should be automatically wrapped when it exceeds the specified width.";

    Bitmap simpleEnglishBitmap = fontRenderer.renderText(
        simpleEnglishText,
        AutoWrapAlignment::LEFT,
        200,  // max_width
        -1,   // max_height
        Color::fromIndex(2),  // white text
        PixelFormat::ARGB1555,
        1,    // outline width
        Color::fromIndex(3),  // red outline
        2,    // char spacing
        SIMPLE_WRAP  // 使用简化版换行
    );

    if (simpleEnglishBitmap.width > 0 && simpleEnglishBitmap.height > 0) {
        std::cout << "  ✓ Simple wrap English: " << simpleEnglishBitmap.width << "x" << simpleEnglishBitmap.height << " pixels" << std::endl;

        std::ofstream file7("test_simple_english.bin", std::ios::binary);
        if (file7.is_open()) {
            file7.write(reinterpret_cast<const char*>(simpleEnglishBitmap.buffer.data()), simpleEnglishBitmap.buffer.size());
            file7.close();

            BinToBMP converter;
            converter.convert("test_simple_english.bin", "test_simple_english.bmp",
                            simpleEnglishBitmap.width, simpleEnglishBitmap.height, PixelFormat::ARGB1555, "");
        }
    } else {
        std::cout << "  ✗ Simple wrap English failed" << std::endl;
    }

    // 测试8: 简化版换行（中英文混合）
    std::cout << "\nTest 8: Simple wrap Chinese-English mixed text" << std::endl;
    std::string simpleMixedText = "这是一个包含中文和English words的混合文本，应该能够正确地进行自动换行处理。";

    Bitmap simpleMixedBitmap = fontRenderer.renderText(
        simpleMixedText,
        AutoWrapAlignment::LEFT,
        180,  // max_width
        -1,   // max_height
        Color::fromIndex(2),  // white text
        PixelFormat::ARGB1555,
        1,    // outline width
        Color::fromIndex(3),  // red outline
        1,    // char spacing
        SIMPLE_WRAP  // 使用简化版换行
    );

    if (simpleMixedBitmap.width > 0 && simpleMixedBitmap.height > 0) {
        std::cout << "  ✓ Simple wrap Mixed: " << simpleMixedBitmap.width << "x" << simpleMixedBitmap.height << " pixels" << std::endl;

        std::ofstream file8("test_simple_mixed.bin", std::ios::binary);
        if (file8.is_open()) {
            file8.write(reinterpret_cast<const char*>(simpleMixedBitmap.buffer.data()), simpleMixedBitmap.buffer.size());
            file8.close();

            BinToBMP converter;
            converter.convert("test_simple_mixed.bin", "test_simple_mixed.bmp",
                            simpleMixedBitmap.width, simpleMixedBitmap.height, PixelFormat::ARGB1555, "");
        }
    } else {
        std::cout << "  ✗ Simple wrap Mixed failed" << std::endl;
    }

    std::cout << "\n=== All tests completed! ===\n" << std::endl;
    std::cout << "Generated test files:" << std::endl;
    std::cout << "  - test_autowrap_english.bin/.bmp (WORD_WRAP mode)" << std::endl;
    std::cout << "  - test_autowrap_mixed.bin/.bmp (WORD_WRAP mode)" << std::endl;
    std::cout << "  - test_array_left.bin/.bmp" << std::endl;
    std::cout << "  - test_array_right.bin/.bmp" << std::endl;
    std::cout << "  - test_i4_autowrap.bin/.bmp" << std::endl;
    std::cout << "  - test_thai_combining.bin/.bmp" << std::endl;
    std::cout << "  - test_simple_english.bin/.bmp (SIMPLE_WRAP mode)" << std::endl;
    std::cout << "  - test_simple_mixed.bin/.bmp (SIMPLE_WRAP mode)" << std::endl;
}

int main(int argc, char* argv[]) {
    // Parse command line arguments
    CommandLine cmdLine;
    CommandLineOptions options = cmdLine.parse(argc, argv);

    // 检查是否是测试模式
    if (argc == 2 && std::string(argv[1]) == "--test") {
        // 测试模式：使用默认字体进行测试
        FontRenderer fontRenderer;
        if (!fontRenderer.initialize()) {
            std::cerr << "Error initializing font renderer" << std::endl;
            return 1;
        }

        // 尝试加载默认字体
        std::string defaultFont = "fonts/DroidSans.ttf";
        if (!fontRenderer.loadFont(defaultFont, 24)) {
            std::cerr << "Error loading default font: " << defaultFont << std::endl;
            std::cerr << "Please make sure the font file exists or specify a valid font path." << std::endl;
            return 1;
        }

        // 运行测试
        runTests(fontRenderer);
        return 0;
    }

    // Show help if requested or if required options are missing
    if (options.help || options.fontPath.empty() || options.text.empty() || options.outputPath.empty()) {
        cmdLine.printHelp();
        std::cout << "\nAdditional options:" << std::endl;
        std::cout << "  --test                       Run comprehensive tests for new features" << std::endl;
        return options.help ? 0 : 1;
    }

    // 不再需要检查I4格式是否提供调色板文件，因为我们现在使用默认调色板

    // Initialize font renderer
    FontRenderer fontRenderer;
    if (!fontRenderer.initialize()) {
        std::cerr << "Error initializing font renderer" << std::endl;
        return 1;
    }

    // Load font
    if (!fontRenderer.loadFont(options.fontPath, options.fontSize)) {
        std::cerr << "Error loading font: " << options.fontPath << std::endl;
        return 1;
    }

    // 初始化全局调色板
    if (!options.palettePath.empty()) {
        // 尝试从文件加载调色板
        if (!loadGlobalPaletteFromFile(options.palettePath)) {
            std::cerr << "Warning: Failed to load palette file: " << options.palettePath << std::endl;
            std::cerr << "Using default palette instead." << std::endl;
            // 加载失败时使用默认调色板
            initGlobalPalette();
        }
    } else {
        // 没有指定调色板文件，使用默认调色板
        initGlobalPalette();
    }

    // 如果是I4格式但没有指定调色板文件，发出警告
    if (options.format == PixelFormat::I4 && options.palettePath.empty()) {
        std::cerr << "Warning: Using I4 format without a palette file. Using default palette." << std::endl;
    }

    // 准备颜色
    Color textColor = options.textColor;
    Color outlineColor = options.outlineColor;

    // 如果指定了索引，设置颜色的paletteIndex属性
    if (options.textColorIndex >= 0) {
        textColor.paletteIndex = options.textColorIndex;
    }

    if (options.outlineColorIndex >= 0) {
        outlineColor.paletteIndex = options.outlineColorIndex;
    }

    // 在渲染前获取最终bitmap的宽度和高度
    int width, height;
    if (fontRenderer.getTextDimensions(options.text, options.outlineWidth, width, height, options.charSpacing)) {
        std::cout << "Text dimensions: " << width << "x" << height << " pixels" << std::endl;
    }

    // 直接渲染到目标格式
    Bitmap bitmap = fontRenderer.renderText(
        options.text,
        textColor,
        options.format, // 直接渲染到目标格式
        options.outlineWidth,
        outlineColor,
        options.charSpacing
    );

    if (bitmap.width == 0 || bitmap.height == 0) {
        std::cerr << "Error rendering text" << std::endl;
        return 1;
    }

    // 保存输出
    std::ofstream file(options.outputPath, std::ios::binary);
    if (!file.is_open()) {
        std::cerr << "Error opening output file: " << options.outputPath << std::endl;
        return 1;
    }

    file.write(reinterpret_cast<const char*>(bitmap.buffer.data()), bitmap.buffer.size());
    file.close();

    // Print information about the generated bitmap
    std::cout << "Generated bitmap:" << std::endl;
    std::cout << "  Text: " << options.text << std::endl;
    std::cout << "  Dimensions: " << bitmap.width << "x" << bitmap.height << " pixels" << std::endl;
    std::cout << "  Format: " << (options.format == PixelFormat::ARGB1555 ? "ARGB1555" : "I4") << std::endl;
    std::cout << "  Output: " << options.outputPath << std::endl;

    // 如果需要输出BMP文件
    if (options.outputBmp) {
        // 创建BinToBMP对象
        BinToBMP converter;

        // 转换并保存BMP文件
        if (!converter.convert(options.outputPath, options.bmpOutputPath, bitmap.width, bitmap.height, options.format, options.palettePath)) {
            std::cerr << "Error converting to BMP: " << options.bmpOutputPath << std::endl;
            return 1;
        }

        std::cout << "  BMP Output: " << options.bmpOutputPath << std::endl;
    }

    return 0;
}
