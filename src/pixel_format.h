#ifndef PIXEL_FORMAT_H
#define PIXEL_FORMAT_H

#include <array>
#include <string>

// 前向声明Color结构体
struct Color;

// 像素格式定义
enum PixelFormat {
    ARGB8888,  // 32位，每通道8位
    ARGB1555,  // 16位，1位alpha，每RGB通道5位
    I4         // 4位索引格式，使用16色调色板
};

// I4格式使用16色调色板
const int PALETTE_SIZE = 16;

// 全局调色板声明（在pixel_format.cpp中定义）
extern std::array<Color, PALETTE_SIZE> g_globalPalette;

// 全局ARGB1555颜色映射（在pixel_format.cpp中定义）
extern std::array<unsigned short, PALETTE_SIZE> g_globalARGB1555Palette;

// 初始化全局调色板
void initGlobalPalette();

// 从文件加载全局调色板
bool loadGlobalPaletteFromFile(const std::string& palettePath);



#endif // PIXEL_FORMAT_H
