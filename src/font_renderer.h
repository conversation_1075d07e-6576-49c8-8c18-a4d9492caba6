#ifndef FONT_RENDERER_H
#define FONT_RENDERER_H

#include <ft2build.h>
#include FT_FREETYPE_H
#include FT_CACHE_H
#include <string>
#include <vector>
#include <memory>
#include <array>
#include <unordered_map>
#include "pixel_format.h"

struct Color {
    unsigned char a;
    unsigned char r;
    unsigned char g;
    unsigned char b;
    int paletteIndex;  // 调色板索引

    // 使用ARGB值创建颜色
    Color(unsigned char a = 255, unsigned char r = 255, unsigned char g = 255, unsigned char b = 255)
        : a(a), r(r), g(g), b(b), paletteIndex(-1) {}

    // 使用调色板索引创建颜色
    static Color fromIndex(int index) {
        Color c;
        c.paletteIndex = index;
        if (index >= 0 && index < PALETTE_SIZE) {
            c.a = g_globalPalette[index].a;
            c.r = g_globalPalette[index].r;
            c.g = g_globalPalette[index].g;
            c.b = g_globalPalette[index].b;
        }
        return c;
    }
};

enum AutoWrapAlignment {
    LEFT,
    RIGHT
};

// 换行模式
enum WrapMode {
    WORD_WRAP,      // 完善版：英文按单词换行，中文按字符换行
    SIMPLE_WRAP     // 简化版：中英文都可以在任意位置换行
};

// 字形缓存信息
struct GlyphInfo {
    int advance;            // 字符前进距离
    int width;              // 字形宽度
    int height;             // 字形高度
    int bearingX;           // 水平偏移
    int bearingY;           // 垂直偏移
    bool isCombining;       // 是否为组合字符

    GlyphInfo() : advance(0), width(0), height(0), bearingX(0), bearingY(0), isCombining(false) {}
    GlyphInfo(int adv, int w, int h, int bx, int by, bool comb)
        : advance(adv), width(w), height(h), bearingX(bx), bearingY(by), isCombining(comb) {}
};

// 文本尺寸缓存信息
struct TextDimensions {
    int totalWidth;
    int totalHeight;
    int maxAscender;
    int maxDescender;

    TextDimensions() : totalWidth(0), totalHeight(0), maxAscender(0), maxDescender(0) {}
    TextDimensions(int w, int h, int a, int d)
        : totalWidth(w), totalHeight(h), maxAscender(a), maxDescender(d) {}
};

// 行信息结构体
struct LineInfo {
    std::string text;        // 行文本
    int width;              // 行宽度
    int height;             // 行高度
    int ascender;           // 上升高度
    int descender;          // 下降高度

    LineInfo() : width(0), height(0), ascender(0), descender(0) {}
    LineInfo(const std::string& t, int w, int h, int a, int d)
        : text(t), width(w), height(h), ascender(a), descender(d) {}
};

struct Bitmap {
    int width;
    int height;
    std::vector<unsigned char> buffer;
    int pitch;  // bytes per row
    PixelFormat format;

    Bitmap() : width(0), height(0), pitch(0), format(PixelFormat::ARGB8888) {}

    Bitmap(int w, int h, PixelFormat fmt = PixelFormat::ARGB8888)
        : width(w), height(h), format(fmt) {
        // 根据像素格式计算每像素字节数和缓冲区大小
        int bytesPerPixel;
        switch (fmt) {
            case PixelFormat::ARGB8888:
                bytesPerPixel = 4;
                buffer.resize(w * h * bytesPerPixel, 0);
                pitch = w * bytesPerPixel;
                break;
            case PixelFormat::ARGB1555:
                bytesPerPixel = 2;
                buffer.resize(w * h * bytesPerPixel, 0);
                pitch = w * bytesPerPixel;
                break;
            case PixelFormat::I4:
                // I4格式每像素4位，每字节2个像素
                bytesPerPixel = 1;
                pitch = (w + 1) / 2;  // 向上取整
                buffer.resize(pitch * h, 0);
                break;
        }
    }
};

class FontRenderer {
public:
    FontRenderer();
    ~FontRenderer();

    bool initialize();
    bool loadFont(const std::string& fontPath, int fontSize);

    // 在渲染前获取最终bitmap的宽度和高度
    bool getTextDimensions(const std::string& text, int outlineWidth, int& width, int& height, int charSpacing = 0);

    // 获取自动换行文本的尺寸
    bool getTextDimensions(const std::string& text, AutoWrapAlignment align, int max_width, int max_height,
                          int outlineWidth, int& width, int& height, int charSpacing = 0, WrapMode wrapMode = WORD_WRAP);

    // 获取字符串数组的尺寸
    bool getTextDimensions(const std::vector<std::string>& textLines, AutoWrapAlignment align,
                          int outlineWidth, int& width, int& height, int charSpacing = 0);

    // 直接渲染到指定格式的位图
    Bitmap renderText(const std::string& text, const Color& color,
                      PixelFormat format = PixelFormat::ARGB8888,
                      int outlineWidth = 1,
                      const Color& outlineColor = Color(255, 255, 0, 0),
                      int charSpacing = 0);

    // 将一个字符串渲染到指定格式的位图
    // 超过宽度的部分自动换行，通过参数指定换行时为左对齐还是右对齐
    // 超过高度的部分会被截断，高度指定为-1时表示不限制
    // wrapMode: WORD_WRAP(完善版) 或 SIMPLE_WRAP(简化版)
    Bitmap renderText(const std::string& text, AutoWrapAlignment align, int max_width, int max_height, const Color& color,
                      PixelFormat format = PixelFormat::ARGB8888,
                      int outlineWidth = 1,
                      const Color& outlineColor = Color(255, 255, 0, 0),
                      int charSpacing = 0,
                      WrapMode wrapMode = WORD_WRAP);

    // 将一个字符串数组渲染到指定格式的位图
    // 数组中每一个字符串渲染为一行，通过参数指定换行时为左对齐还是右对齐
    Bitmap renderText(const std::vector<std::string>& text, AutoWrapAlignment align, const Color& color,
                      PixelFormat format = PixelFormat::ARGB8888,
                      int outlineWidth = 1,
                      const Color& outlineColor = Color(255, 255, 0, 0),
                      int charSpacing = 0);

    // FreeType 缓存系统管理接口
    void setCacheSize(size_t maxGlyphCacheSize, size_t maxDimensionCacheSize, size_t maxBitmapCacheSize = 200);
    void getCacheSize(size_t& currentGlyphCacheSize, size_t& currentDimensionCacheSize, size_t& currentBitmapCacheSize) const;
    size_t getCacheMemoryUsage() const;  // 获取缓存占用的内存大小（字节）

private:
    FT_Library m_library;
    FT_Face m_face;
    bool m_initialized;
    int m_fontSize;  // 当前字体大小
    std::array<Color, PALETTE_SIZE> m_palette;  // 用于I4格式

    // FreeType 缓存系统
    FTC_Manager m_cacheManager;
    FTC_CMapCache m_cmapCache;
    FTC_ImageCache m_imageCache;
    FTC_SBitCache m_sbitCache;
    bool m_cacheInitialized;

    // 性能优化：字形缓存
    std::unordered_map<FT_UInt, GlyphInfo> m_glyphCache;
    mutable std::unordered_map<std::string, TextDimensions> m_dimensionCache;  // 文本尺寸缓存

    // 从 UTF-8 编码的字符串中解码单个字符
    int decodeUTF8Char(const std::string& text, size_t index, FT_UInt& charCode);

    // 检查字符是否是组合字符（如泰文的上标、下标符号）
    bool isCombiningCharacter(FT_UInt charCode);

    // 计算文本尺寸的辅助方法
    bool calculateTextDimensions(const std::string& text, int outlineWidth, int& totalWidth, int& totalHeight, int& maxAscender, int& maxDescender, int charSpacing = 0);

    // 渲染单个字符到ARGB8888格式
    void renderGlyphARGB8888(FT_GlyphSlot glyph, Bitmap& bitmap, int& penX, int& penY, const Color& color);

    // 渲染单个字符到ARGB1555格式
    void renderGlyphARGB1555(FT_GlyphSlot glyph, Bitmap& bitmap, int& penX, int& penY, const Color& color);

    // 渲染单个字符到I4格式
    void renderGlyphI4(FT_GlyphSlot glyph, Bitmap& bitmap, int& penX, int& penY, const Color& color);

    // 添加描边（根据位图格式使用不同的实现）
    void addOutline(Bitmap& bitmap, int outlineWidth, const Color& outlineColor);

    // 添加ARGB8888格式的描边
    void addOutlineARGB8888(Bitmap& bitmap, int outlineWidth, const Color& outlineColor);

    // 添加ARGB1555格式的描边
    void addOutlineARGB1555(Bitmap& bitmap, int outlineWidth, const Color& outlineColor);

    // 添加I4格式的描边
    void addOutlineI4(Bitmap& bitmap, int outlineWidth, const Color& outlineColor);

    // 将源位图复制到目标位图的指定位置
    void copyBitmapToTarget(const Bitmap& source, Bitmap& target, int offsetX, int offsetY);

    // 检查字符是否是中文等宽字符
    bool isFullWidthCharacter(FT_UInt charCode);

    // 将文本分解为行信息（支持自动换行）
    std::vector<LineInfo> breakTextIntoLines(const std::string& text, int maxWidth, int maxHeight,
                                             int outlineWidth, int charSpacing);

    // 简化版换行：中英文都可以在任意位置换行
    std::vector<LineInfo> breakTextIntoLinesSimple(const std::string& text, int maxWidth, int maxHeight,
                                                  int outlineWidth, int charSpacing);

    // 将字符串数组转换为行信息
    std::vector<LineInfo> convertToLineInfo(const std::vector<std::string>& textLines,
                                           int outlineWidth, int charSpacing);

    // 渲染行信息列表到位图（可选最大宽度限制，-1表示不限制）
    Bitmap renderLineInfos(const std::vector<LineInfo>& lineInfos, AutoWrapAlignment align,
                          const Color& color, PixelFormat format,
                          int outlineWidth, const Color& outlineColor, int charSpacing, int maxWidth = -1);

    // 检查单词是否需要分割
    bool needsWordBreaking(const std::string& word, int maxWidth, int outlineWidth, int charSpacing);

    // 分割过长的单词
    void breakLongWord(const std::string& word, int maxWidth, int outlineWidth, int charSpacing, std::vector<LineInfo>& lines);

    // 性能优化方法
    const GlyphInfo& getGlyphInfo(FT_UInt charCode);  // 获取字形信息（带缓存）
    std::string generateCacheKey(const std::string& text, int outlineWidth, int charSpacing) const;  // 生成缓存键

    // FreeType 缓存系统相关方法
    bool initializeCache();
    static FT_Error faceRequester(FTC_FaceID face_id, FT_Library library, FT_Pointer request_data, FT_Face* aface);
    FT_UInt getCharIndex(FT_UInt charCode);
    FT_Glyph getCachedGlyph(FT_UInt charCode, int pixelSize);
    FTC_SBit getCachedSBit(FT_UInt charCode, int pixelSize);
    void renderCachedGlyphToTarget(FTC_SBit sbit, Bitmap& target, int penX, int penY, const Color& color);
    void renderSBitARGB8888(FTC_SBit sbit, Bitmap& bitmap, int left, int top, const Color& color);
    void renderSBitARGB1555(FTC_SBit sbit, Bitmap& bitmap, int left, int top, const Color& color);
    void renderSBitI4(FTC_SBit sbit, Bitmap& bitmap, int left, int top, const Color& color);


};

#endif // FONT_RENDERER_H
