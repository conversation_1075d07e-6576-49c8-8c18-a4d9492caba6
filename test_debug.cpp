#include "font_renderer.h"
#include <iostream>

int main() {
    FontRenderer fontRenderer;
    if (!fontRenderer.initialize()) {
        std::cerr << "Error initializing font renderer" << std::endl;
        return 1;
    }
    
    if (!fontRenderer.loadFont("../fonts/DroidSans.ttf", 24)) {
        std::cerr << "Error loading font" << std::endl;
        return 1;
    }
    
    initGlobalPalette();
    
    std::string text = "这是一个包含中文和English words的混合文本";
    int maxWidth = 180;
    
    // 直接调用 breakTextIntoLines 来查看结果
    std::vector<LineInfo> lines = fontRenderer.breakTextIntoLines(text, maxWidth, -1, 1, 1);
    
    std::cout << "Break result:" << std::endl;
    for (size_t i = 0; i < lines.size(); i++) {
        std::cout << "Line " << i << ": \"" << lines[i].text << "\" width: " << lines[i].width << std::endl;
    }
    
    return 0;
}
