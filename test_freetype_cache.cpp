#include <ft2build.h>
#include FT_FREETYPE_H
#include FT_CACHE_H
#include <iostream>

int main() {
    FT_Library library;
    FT_Error error = FT_Init_FreeType(&library);
    if (error) {
        std::cerr << "Failed to initialize FreeType: " << error << std::endl;
        return 1;
    }
    
    std::cout << "FreeType initialized successfully" << std::endl;
    
    // 测试缓存管理器创建
    FTC_Manager manager;
    error = FTC_Manager_New(library, 0, 0, 1024*1024, nullptr, nullptr, &manager);
    if (error) {
        std::cerr << "Failed to create FTC_Manager: " << error << std::endl;
        FT_Done_FreeType(library);
        return 1;
    }
    
    std::cout << "FreeType cache manager created successfully" << std::endl;
    
    FTC_Manager_Done(manager);
    FT_Done_FreeType(library);
    
    std::cout << "FreeType cache system test passed!" << std::endl;
    return 0;
}
