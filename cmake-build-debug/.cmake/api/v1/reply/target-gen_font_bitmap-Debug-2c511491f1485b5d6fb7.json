{"artifacts": [{"path": "gen_font_bitmap"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "link_directories", "target_link_libraries", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 34, "parent": 0}, {"command": 1, "file": 0, "line": 41, "parent": 0}, {"command": 2, "file": 0, "line": 12, "parent": 0}, {"command": 3, "file": 0, "line": 38, "parent": 0}, {"command": 4, "file": 0, "line": 11, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu++14 -fdiagnostics-color=always"}], "includes": [{"backtrace": 5, "path": "/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/include/freetype2"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "14"}, "sourceIndexes": [0, 1, 2, 3, 4, 5]}], "id": "gen_font_bitmap::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "bin"}], "prefix": {"path": "/usr/local"}}, "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"backtrace": 3, "fragment": "-L/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/lib", "role": "libraryPath"}, {"fragment": "-Wl,-rpath,/home/<USER>/workspace/projects/local/gen_font_bitmap/thirdpart/freetype-2.13.3/output/lib:", "role": "libraries"}, {"backtrace": 4, "fragment": "-lfreetype", "role": "libraries"}], "language": "CXX"}, "name": "gen_font_bitmap", "nameOnDisk": "gen_font_bitmap", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [6, 7, 8, 9, 10]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/font_renderer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/bitmap_converter.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/command_line.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/pixel_format.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/bin2bmp.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/font_renderer.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/bitmap_converter.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/command_line.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/pixel_format.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/bin2bmp.h", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}