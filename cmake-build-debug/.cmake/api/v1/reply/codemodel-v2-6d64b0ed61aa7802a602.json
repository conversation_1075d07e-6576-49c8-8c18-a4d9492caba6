{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-960a8606440dc5ac71fa.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "gen_font_bitmap", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "bin2bmp::@6890427a1f51a3e7e1df", "jsonFile": "target-bin2bmp-Debug-d13d1f37fbf712dd3a70.json", "name": "bin2bmp", "projectIndex": 0}, {"directoryIndex": 0, "id": "gen_font_bitmap::@6890427a1f51a3e7e1df", "jsonFile": "target-gen_font_bitmap-Debug-2c511491f1485b5d6fb7.json", "name": "gen_font_bitmap", "projectIndex": 0}, {"directoryIndex": 0, "id": "vz_font::@6890427a1f51a3e7e1df", "jsonFile": "target-vz_font-Debug-30a5049c5d6229679681.json", "name": "vz_font", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/workspace/projects/local/gen_font_bitmap/cmake-build-debug", "source": "/home/<USER>/workspace/projects/local/gen_font_bitmap"}, "version": {"major": 2, "minor": 6}}