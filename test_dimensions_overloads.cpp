#include "font_renderer.h"
#include <iostream>
#include <vector>

int main() {
    FontRenderer fontRenderer;
    if (!fontRenderer.initialize()) {
        std::cerr << "Failed to initialize font renderer" << std::endl;
        return 1;
    }
    
    if (!fontRenderer.loadFont("fonts/DroidSans.ttf", 24)) {
        std::cerr << "Failed to load font" << std::endl;
        return 1;
    }
    
    initGlobalPalette();
    
    std::cout << "Testing getTextDimensions overloads:" << std::endl;
    std::cout << "====================================" << std::endl;
    
    // 测试1：单行文本
    std::cout << "\n1. Single line text:" << std::endl;
    std::string singleText = "Hello World 中文测试";
    int width1, height1;
    if (fontRenderer.getTextDimensions(singleText, 1, width1, height1, 0)) {
        std::cout << "   Text: \"" << singleText << "\"" << std::endl;
        std::cout << "   Dimensions: " << width1 << "x" << height1 << " pixels" << std::endl;
    }
    
    // 测试2：自动换行文本
    std::cout << "\n2. Auto-wrap text:" << std::endl;
    std::string wrapText = "This is a long text that should wrap automatically when the width limit is reached. 这是一个很长的文本，当达到宽度限制时应该自动换行。";
    int width2, height2;
    if (fontRenderer.getTextDimensions(wrapText, AutoWrapAlignment::LEFT, 200, -1, 1, width2, height2, 0, WORD_WRAP)) {
        std::cout << "   Text: \"" << wrapText.substr(0, 50) << "...\"" << std::endl;
        std::cout << "   Max width: 200 pixels" << std::endl;
        std::cout << "   Dimensions: " << width2 << "x" << height2 << " pixels" << std::endl;
    }
    
    // 测试3：简化换行模式
    int width3, height3;
    if (fontRenderer.getTextDimensions(wrapText, AutoWrapAlignment::LEFT, 200, -1, 1, width3, height3, 0, SIMPLE_WRAP)) {
        std::cout << "   Simple wrap mode: " << width3 << "x" << height3 << " pixels" << std::endl;
    }
    
    // 测试4：字符串数组
    std::cout << "\n3. String array:" << std::endl;
    std::vector<std::string> textArray = {
        "Line 1: Hello",
        "Line 2: 中文测试",
        "Line 3: Mixed 混合 text",
        "Line 4: Final line"
    };
    int width4, height4;
    if (fontRenderer.getTextDimensions(textArray, AutoWrapAlignment::LEFT, 1, width4, height4, 0)) {
        std::cout << "   Array with " << textArray.size() << " lines" << std::endl;
        std::cout << "   Dimensions: " << width4 << "x" << height4 << " pixels" << std::endl;
    }
    
    // 验证：对比渲染结果的尺寸
    std::cout << "\n4. Verification (comparing with actual rendering):" << std::endl;
    
    // 验证单行文本
    Bitmap bitmap1 = fontRenderer.renderText(singleText, Color::fromIndex(2), PixelFormat::ARGB1555, 1, Color::fromIndex(3), 0);
    std::cout << "   Single text - Calculated: " << width1 << "x" << height1 << ", Actual: " << bitmap1.width << "x" << bitmap1.height;
    if (width1 == bitmap1.width && height1 == bitmap1.height) {
        std::cout << " ✓ MATCH" << std::endl;
    } else {
        std::cout << " ✗ MISMATCH" << std::endl;
    }
    
    // 验证自动换行文本
    Bitmap bitmap2 = fontRenderer.renderText(wrapText, AutoWrapAlignment::LEFT, 200, -1, Color::fromIndex(2), PixelFormat::ARGB1555, 1, Color::fromIndex(3), 0, WORD_WRAP);
    std::cout << "   Auto-wrap text - Calculated: " << width2 << "x" << height2 << ", Actual: " << bitmap2.width << "x" << bitmap2.height;
    if (width2 == bitmap2.width && height2 == bitmap2.height) {
        std::cout << " ✓ MATCH" << std::endl;
    } else {
        std::cout << " ✗ MISMATCH" << std::endl;
    }
    
    // 验证字符串数组
    Bitmap bitmap4 = fontRenderer.renderText(textArray, AutoWrapAlignment::LEFT, Color::fromIndex(2), PixelFormat::ARGB1555, 1, Color::fromIndex(3), 0);
    std::cout << "   String array - Calculated: " << width4 << "x" << height4 << ", Actual: " << bitmap4.width << "x" << bitmap4.height;
    if (width4 == bitmap4.width && height4 == bitmap4.height) {
        std::cout << " ✓ MATCH" << std::endl;
    } else {
        std::cout << " ✗ MISMATCH" << std::endl;
    }
    
    std::cout << "\n✓ All getTextDimensions overloads tested successfully!" << std::endl;
    
    return 0;
}
