#include "font_renderer.h"
#include <iostream>

int main() {
    FontRenderer fontRenderer;
    if (!fontRenderer.initialize()) {
        std::cerr << "Error initializing font renderer" << std::endl;
        return 1;
    }
    
    if (!fontRenderer.loadFont("../fonts/DroidSans.ttf", 24)) {
        std::cerr << "Error loading font" << std::endl;
        return 1;
    }
    
    initGlobalPalette();
    
    std::string text = "This is a long English sentence with 中文字符 mixed together.";
    int maxWidth = 200;
    
    // 完善版换行（英文按单词，中文按字符）
    Bitmap wordWrapBitmap = fontRenderer.renderText(
        text,
        AutoWrapAlignment::LEFT,
        maxWidth, -1,
        Color::fromIndex(2),
        PixelFormat::ARGB1555,
        1, Color::fromIndex(3), 0,
        WORD_WRAP  // 完善版
    );
    
    // 简化版换行（中英文都可以任意位置换行）
    Bitmap simpleWrapBitmap = fontRenderer.renderText(
        text,
        AutoWrapAlignment::LEFT,
        maxWidth, -1,
        Color::fromIndex(2),
        PixelFormat::ARGB1555,
        1, Color::fromIndex(3), 0,
        SIMPLE_WRAP  // 简化版
    );
    
    std::cout << "Text: \"" << text << "\"" << std::endl;
    std::cout << "Max width: " << maxWidth << " pixels" << std::endl;
    std::cout << std::endl;
    std::cout << "WORD_WRAP mode result: " << wordWrapBitmap.width << "x" << wordWrapBitmap.height << " pixels" << std::endl;
    std::cout << "SIMPLE_WRAP mode result: " << simpleWrapBitmap.width << "x" << simpleWrapBitmap.height << " pixels" << std::endl;
    
    return 0;
}
