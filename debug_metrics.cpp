#include "font_renderer.h"
#include <iostream>
#include <iomanip>

int main() {
    FontRenderer fontRenderer;
    if (!fontRenderer.initialize()) {
        std::cerr << "Failed to initialize font renderer" << std::endl;
        return 1;
    }
    
    if (!fontRenderer.loadFont("../fonts/DroidSans.ttf", 24)) {
        std::cerr << "Failed to load font" << std::endl;
        return 1;
    }
    
    initGlobalPalette();
    
    // 测试不同的文本
    std::vector<std::string> testTexts = {
        "ABC",
        "中文",
        "中文ABC",
        "gjpqy",  // 有下降部分的字符
        "ÀÁÂÃÄÅ",  // 有上升部分的字符
        "测试顶部"
    };
    
    std::cout << "Font metrics analysis:" << std::endl;
    std::cout << "======================" << std::endl;
    
    for (const auto& text : testTexts) {
        int width, height;
        if (fontRenderer.getTextDimensions(text, 1, width, height, 0)) {
            std::cout << "Text: \"" << text << "\"" << std::endl;
            std::cout << "  Dimensions: " << width << "x" << height << " pixels" << std::endl;
            
            // 渲染并检查实际使用的空间
            Bitmap bitmap = fontRenderer.renderText(text, Color::fromIndex(2), 
                                                   PixelFormat::ARGB1555, 1, Color::fromIndex(3), 0);
            std::cout << "  Bitmap size: " << bitmap.width << "x" << bitmap.height << " pixels" << std::endl;
            
            // 分析位图中实际有内容的区域
            int topMostPixel = bitmap.height;
            int bottomMostPixel = -1;
            
            for (int y = 0; y < bitmap.height; y++) {
                for (int x = 0; x < bitmap.width; x++) {
                    int pixelIndex = (y * bitmap.width + x) * 2; // ARGB1555 = 2 bytes per pixel
                    if (pixelIndex + 1 < bitmap.buffer.size()) {
                        uint16_t pixel = (bitmap.buffer[pixelIndex + 1] << 8) | bitmap.buffer[pixelIndex];
                        if (pixel != 0) { // 非透明像素
                            topMostPixel = std::min(topMostPixel, y);
                            bottomMostPixel = std::max(bottomMostPixel, y);
                        }
                    }
                }
            }
            
            if (bottomMostPixel >= 0) {
                int actualContentHeight = bottomMostPixel - topMostPixel + 1;
                int topPadding = topMostPixel;
                int bottomPadding = bitmap.height - bottomMostPixel - 1;
                
                std::cout << "  Content analysis:" << std::endl;
                std::cout << "    Top padding: " << topPadding << " pixels" << std::endl;
                std::cout << "    Content height: " << actualContentHeight << " pixels" << std::endl;
                std::cout << "    Bottom padding: " << bottomPadding << " pixels" << std::endl;
                std::cout << "    Top/Bottom ratio: " << std::fixed << std::setprecision(2) 
                          << (float)topPadding / (topPadding + bottomPadding) << std::endl;
            } else {
                std::cout << "  No visible content found" << std::endl;
            }
            
            std::cout << std::endl;
        }
    }
    
    return 0;
}
