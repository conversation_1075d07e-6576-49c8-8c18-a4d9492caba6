cmake_minimum_required(VERSION 3.10)
project(gen_font_bitmap VERSION 1.0)

# Set C++ standard
set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find FreeType package
#find_package(Freetype REQUIRED)
#include_directories(${FREETYPE_INCLUDE_DIRS})
include_directories(thirdpart/freetype-2.13.3/output/include/freetype2)
link_directories(thirdpart/freetype-2.13.3/output/lib)

# Add source files
set(SOURCES
    src/main.cpp
    src/font_renderer.cpp
    src/bitmap_converter.cpp
    src/command_line.cpp
    src/pixel_format.cpp
    src/bin2bmp.cpp
)

# Add header files
set(HEADERS
    src/font_renderer.h
    src/bitmap_converter.h
    src/command_line.h
    src/pixel_format.h
    src/bin2bmp.h
)

# Create executable
add_executable(gen_font_bitmap ${SOURCES} ${HEADERS})

# Link libraries
#target_link_libraries(gen_font_bitmap ${FREETYPE_LIBRARIES})
target_link_libraries(gen_font_bitmap -lfreetype)

# Install target
install(TARGETS gen_font_bitmap DESTINATION bin)

# Bin to BMP converter tool
set(BIN2BMP_SOURCES
    src/bin2bmp_main.cpp
    src/bin2bmp.cpp
    src/bitmap_converter.cpp
    src/pixel_format.cpp
)

set(BIN2BMP_HEADERS
    src/bin2bmp.h
    src/bitmap_converter.h
    src/pixel_format.h
)

add_executable(bin2bmp ${BIN2BMP_SOURCES} ${BIN2BMP_HEADERS})
install(TARGETS bin2bmp DESTINATION bin)

# Bin to BMP converter tool
set(VZ_FONT_SOURCES
  src/bin2bmp.cpp
  src/pixel_format.cpp
  vz/chartab.cpp
  vz/vzft.cpp
  vz/char_code.cpp
  vz/char_code_c.c
  vz/vzft.cpp
  vz/vz_font_test.cpp
)

add_executable(vz_font ${VZ_FONT_SOURCES})
target_link_libraries(vz_font -lfreetype)

install(TARGETS vz_font DESTINATION bin)
